import React, { memo } from "react"

import { Content } from "components/Layout"
import { RecurrentImportSettings } from "components/RecurrentImportSettings"
import AppHeader from "components/shared/AppHeader"

import { importExportSettingKeys } from "constants/recurrentSettingsConstants"

const { importTitle, importPermissionCode, importViewPermissionCode } =
  importExportSettingKeys.products

const ProductImportSettingsPage = memo(() => (
  <>
    <AppHeader title={importTitle} />
    <Content>
      <RecurrentImportSettings
        permissionCode={importPermissionCode}
        title={importTitle}
        viewPermissionCode={importViewPermissionCode}
      />
    </Content>
  </>
))

export default ProductImportSettingsPage
