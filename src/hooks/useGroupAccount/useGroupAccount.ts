import { useCallback } from "react"
import { useSelector } from "react-redux"

import { groupAccountOptionsSelector } from "selectors/groupAccountSelectors"

import { GROUP_ACCOUNT_TYPE } from "constants/groupAccount"

import { GroupAccountOptionType } from "types/GroupAccount"

import {
  GetOldSellerGroupIdReturnType,
  GetSellerType,
  UseGroupAccountReturnType,
} from "./useGroupAccountTypes"

export const useGroupAccount = (): UseGroupAccountReturnType => {
  const { options: groupAccountOptions } = useSelector(
    groupAccountOptionsSelector,
  )

  const findSellerOption = useCallback(
    (sellerId: string | undefined): GroupAccountOptionType | undefined => {
      if (!sellerId) {
        return undefined
      }

      return groupAccountOptions.find(
        (option: GroupAccountOptionType) => option.value === sellerId,
      )
    },
    [groupAccountOptions],
  )

  const getOldSellerGroupId = useCallback(
    (seller_id: string | undefined): GetOldSellerGroupIdReturnType => {
      if (!seller_id) {
        return { sellerId: undefined }
      }

      const groupAccountOption = findSellerOption(seller_id)

      // DESC: Typescript doesn't allow to guard with constant
      if (
        !groupAccountOption ||
        groupAccountOption.type === GROUP_ACCOUNT_TYPE.GLOBAL
      ) {
        return { sellerId: undefined }
      }

      const groupAccountTypeValue = {
        [GROUP_ACCOUNT_TYPE.ALL]: { sellerId: GROUP_ACCOUNT_TYPE.ALL },
        [GROUP_ACCOUNT_TYPE.ACCOUNT]: { sellerId: groupAccountOption.value },
        [GROUP_ACCOUNT_TYPE.GROUP]: { groupId: groupAccountOption.value },
      }

      return groupAccountTypeValue[groupAccountOption.type]
    },
    [findSellerOption],
  )

  const getSellerType = useCallback(
    (sellerId: string | undefined): GetSellerType | undefined => {
      if (!sellerId) {
        return GROUP_ACCOUNT_TYPE.GLOBAL
      }

      const selectedSellerOption = findSellerOption(sellerId)

      return selectedSellerOption?.type || undefined
    },
    [findSellerOption],
  )

  return {
    findSellerOption,
    getSellerType,
    getOldSellerGroupId,
  }
}
