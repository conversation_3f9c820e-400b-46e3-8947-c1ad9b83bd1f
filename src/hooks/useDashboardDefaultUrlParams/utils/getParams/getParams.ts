import { checkIsArray, getObjectKeys } from "@develop/fe-library/dist/utils"

import { DASHBOARD_MANDATORY_URL_PARAMS } from "constants/dashboard"

import { DashboardFiltersParams } from "types"

import { GetParamsParams } from "./getParamsTypes"

export const getParams = ({
  sessionUrlParams,
  currentUrlParams,
  initialUrlParams,
}: GetParamsParams): DashboardFiltersParams => {
  const isSessionUrlParamsExist: boolean = checkIsArray(
    getObjectKeys(sessionUrlParams),
  )

  const isCurrentUrlParams: boolean = checkIsArray(
    getObjectKeys(currentUrlParams),
  )
  const isCurrentUrlParamsHaveMandatoryParams: boolean =
    DASHBOARD_MANDATORY_URL_PARAMS.every((param) => currentUrlParams[param])

  if (isSessionUrlParamsExist) {
    // 1. SS, no url - use SS
    const shouldUseSessionUrlParams: boolean =
      !isCurrentUrlParams ||
      (isCurrentUrlParamsHaveMandatoryParams &&
        currentUrlParams.inputMode === sessionUrlParams.inputMode)

    // 2. SS, url
    // 2.1. url has all mandatory params - user inserted link - use url
    const shouldUseCurrentUrlParams: boolean =
      isCurrentUrlParams &&
      isCurrentUrlParamsHaveMandatoryParams &&
      currentUrlParams.inputMode !== sessionUrlParams.inputMode

    // 2.2. url has not all mandatory params - use SS
    const isCurrentUrlParamsBroken: boolean =
      isCurrentUrlParams && !isCurrentUrlParamsHaveMandatoryParams

    if (shouldUseSessionUrlParams || isCurrentUrlParamsBroken) {
      return sessionUrlParams
    } else if (shouldUseCurrentUrlParams) {
      return currentUrlParams
    }
  }

  // 3. no SS, No url - use initial
  const shouldUseInitialUrlParams: boolean = !isCurrentUrlParams

  // 4. no SS, url - use url
  const shouldUseCurrentUrlParams: boolean = isCurrentUrlParams

  if (shouldUseInitialUrlParams) {
    return initialUrlParams
  } else if (shouldUseCurrentUrlParams) {
    return currentUrlParams
  }

  return {}
}
