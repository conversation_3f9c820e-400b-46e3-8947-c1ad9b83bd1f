import {
  DashboardFiltersParams,
  OrdersUrlParams,
  TransactionsUrlParams,
} from "types"

type Product = {
  marketplace_id: string
  seller_sku: string
  seller_id: string
}

export type GetOrderLinkParams = {
  product: Product
  urlParams: DashboardFiltersParams
  ordersUrlParams?: OrdersUrlParams
}

export type GetOrdersLink = (params: GetOrderLinkParams) => string

export type GetTransactionsLinkParams = {
  product: Product
  urlParams: DashboardFiltersParams
  transactionsUrlParams?: TransactionsUrlParams
}

export type GetTransactionsLink = (params: GetTransactionsLinkParams) => string

export type UseGenerateLinkReturnType = {
  getOrdersLink: GetOrdersLink
  getTransactionsLink: GetTransactionsLink
}
