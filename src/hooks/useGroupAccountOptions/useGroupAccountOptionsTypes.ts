import { SellerIdUrlParams } from "types"
import { GroupAccountOptionType } from "types/GroupAccount"

export type FilteredBasAccountsOptionsParams = {
  urlParams: SellerIdUrlParams
}

export type GroupAccountData = {
  options: GroupAccountOptionType[]
  groupAccountOptions: GroupAccountOptionType[]
  basAccountsOptions: GroupAccountOptionType[]
  isOptionsReady: boolean
  isOneBASAccount: boolean
  filteredBasAccountsOptions: (
    params: FilteredBasAccountsOptionsParams,
  ) => GroupAccountOptionType[]
}

export type UseGroupAccountOptionsReturnType = GroupAccountData
