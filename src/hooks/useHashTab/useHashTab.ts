import { useCallback, useMemo } from "react"
import { useHistory } from "react-router-dom"

import {
  SetHashTabParams,
  UseHashTabProps,
  UseHashTabReturnTypes,
} from "./useHashTabTypes"

export const useHashTab = ({
  tabKey,
}: UseHashTabProps): UseHashTabReturnTypes => {
  const history = useHistory()
  const { hash } = history.location

  const hashParams = useMemo(() => new URLSearchParams(hash.slice(1)), [hash])

  const hashTab = hashParams.get(tabKey)

  const setHashTab = useCallback(
    ({ tab, nextSearch }: SetHashTabParams): void => {
      hashParams.set(tabKey, String(tab))

      history.replace({
        ...history.location,
        search: nextSearch ?? history.location.search,
        hash: hashParams.toString(),
      })
    },
    [hashParams, history, tabKey],
  )

  return {
    hashTab,
    setHashTab,
  }
}
