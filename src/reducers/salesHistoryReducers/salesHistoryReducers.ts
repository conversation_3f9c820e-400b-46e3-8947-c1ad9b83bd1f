import { API_STATUS } from "api/constants"

import { types as ActionTypes } from "actions/salesHistoryActions"

import generateReducer from "utils/generateReducer"

import { ASYNC_STATUSES } from "constants/async"

import { SalesHistoryState } from "./salesHistoryReducersTypes"

const initialState: SalesHistoryState = {
  checkedCategoriesState: null,
  products: [],
  productsStatus: ASYNC_STATUSES.IDLE,
  productsTopFilter: [],
  productsTopFilterStatus: ASYNC_STATUSES.IDLE,
  productFilters: {},
  productFiltersStatus: ASYNC_STATUSES.IDLE,
  units: [],
  isChartLoading: true,
  widgetStatistics: {
    metrics: [],
  },
  minStatsDate: null,
  minStatsDateStatus: ASYNC_STATUSES.IDLE,
  updatedAt: null, // Comes from BE
  ppcMetrics: [], // Comes from BE
  dataSeries: [], // Comes from BE
  salesCategories: null, // Comes from BE
}

const { FAILURE, REQUEST, SUCCESS } = API_STATUS

const reductions = {
  [ActionTypes.getMinimumSalesHistoryDate[REQUEST]]: (
    state: SalesHistoryState,
  ) => {
    return {
      ...state,
      minStatsDateStatus: ASYNC_STATUSES.PENDING,
    }
  },
  [ActionTypes.getMinimumSalesHistoryDate[SUCCESS]]: (
    state: SalesHistoryState,
    payload,
  ) => {
    return {
      ...state,
      minStatsDate: payload?.date || null,
      minStatsDateStatus: ASYNC_STATUSES.FULFILLED,
    }
  },
  [ActionTypes.getMinimumSalesHistoryDate[FAILURE]]: (
    state: SalesHistoryState,
  ) => {
    return {
      ...state,
      minStatsDateStatus: ASYNC_STATUSES.REJECTED,
    }
  },

  [ActionTypes.updateCheckedCategoriesState]: (
    state: SalesHistoryState,
    payload = {},
  ) => {
    return {
      ...state,
      checkedCategoriesState: {
        ...state.checkedCategoriesState,
        ...payload,
      },
    }
  },

  // getProducts
  [ActionTypes.getProducts[REQUEST]]: (state: SalesHistoryState) => {
    return {
      ...state,
      productsStatus: ASYNC_STATUSES.PENDING,
    }
  },
  [ActionTypes.getProducts[SUCCESS]]: (
    state: SalesHistoryState,
    { products = [] },
  ) => {
    return {
      ...state,
      products,
      productsStatus: ASYNC_STATUSES.FULFILLED,
    }
  },
  [ActionTypes.getProducts[FAILURE]]: (state: SalesHistoryState) => {
    return {
      ...state,
      productsStatus: ASYNC_STATUSES.REJECTED,
    }
  },

  [ActionTypes.clearProducts]: (state: SalesHistoryState) => {
    return {
      ...state,
      products: [],
      productsStatus: ASYNC_STATUSES.IDLE,
    }
  },

  // getProductsTopFilter
  [ActionTypes.getProductsTopFilter[REQUEST]]: (state: SalesHistoryState) => {
    return {
      ...state,
      productsTopFilterStatus: ASYNC_STATUSES.PENDING,
    }
  },
  [ActionTypes.getProductsTopFilter[SUCCESS]]: (
    state: SalesHistoryState,
    { products = [] },
  ) => {
    return {
      ...state,
      productsTopFilter: products,
      productsTopFilterStatus: ASYNC_STATUSES.FULFILLED,
    }
  },
  [ActionTypes.getProductsTopFilter[FAILURE]]: (state: SalesHistoryState) => {
    return {
      ...state,
      productsTopFilterStatus: ASYNC_STATUSES.REJECTED,
    }
  },
  [ActionTypes.clearProductsTopFilter]: (state: SalesHistoryState) => {
    return {
      ...state,
      productsTopFilter: [],
      productsTopFilterStatus: ASYNC_STATUSES.IDLE,
    }
  },

  // getProductFilters
  [ActionTypes.getProductFilters[REQUEST]]: (state: SalesHistoryState) => {
    return {
      ...state,
      productFiltersStatus: ASYNC_STATUSES.PENDING,
    }
  },
  [ActionTypes.getProductFilters[SUCCESS]]: (
    state: SalesHistoryState,
    payload,
  ) => {
    return {
      ...state,
      productFilters: { ...payload },
      productFiltersStatus: ASYNC_STATUSES.FULFILLED,
    }
  },
  [ActionTypes.getProductFilters[FAILURE]]: (state: SalesHistoryState) => {
    return {
      ...state,
      productFiltersStatus: ASYNC_STATUSES.REJECTED,
    }
  },
  [ActionTypes.clearProductFilters]: (state: SalesHistoryState) => {
    return {
      ...state,
      productFilters: {},
      productFiltersStatus: ASYNC_STATUSES.IDLE,
    }
  },

  [ActionTypes.getChartData[FAILURE]]: (state: SalesHistoryState) => {
    return {
      ...state,
      isChartLoading: false,
    }
  },

  [ActionTypes.getChartData[REQUEST]]: (state: SalesHistoryState) => {
    return {
      ...state,
      isChartLoading: true,
    }
  },

  [ActionTypes.getChartData[SUCCESS]]: (state: SalesHistoryState, payload) => {
    return {
      ...state,
      ...payload,
      isChartLoading: false,
    }
  },

  [ActionTypes.getWidgetStatisticsData[SUCCESS]]: (
    state: SalesHistoryState,
    payload,
  ) => {
    return {
      ...state,
      widgetStatistics: payload,
    }
  },
}

export default generateReducer(initialState, reductions)
