import { transactionsInitialState } from "initialState"

import { types as ActionTypes } from "actions/transactionsActions"

import generateReducer from "utils/generateReducer"

const reductions = {
  [ActionTypes.updateUrlParams]: (state, urlParams) => ({
    ...state,
    urlParams: {
      ...state.urlParams,
      ...urlParams,
    },
  }),
  [ActionTypes.changeSearchOptions]: (state, searchOptions) => ({
    ...state,
    searchOptions,
    prevSearchOptions: state.searchOptions,
  }),
  [ActionTypes.clearSearchOptions]: (state) => ({
    ...state,
    searchOptions: {},
    prevSearchOptions: {},
  }),
  [ActionTypes.displayModal]: (
    state,
    { modalVisible, initialValues = {} },
  ) => ({
    ...state,
    modalVisible,
    initialValues,
  }),
  [ActionTypes.showConfirmationModal]: (
    state,
    { confirmationModalVisible, initialValues = {} },
  ) => ({
    ...state,
    confirmationModalVisible,
    initialValues,
  }),
  [ActionTypes.getTransactions[0]]: (state) => ({
    ...state,
    isTransactionsDataLoading: true,
  }),
  [ActionTypes.getTransactions[1]]: (state, { totalCount, data }) => ({
    ...state,
    payments: data,
    totalCount,
    isTransactionsDataLoading: false,
  }),
  [ActionTypes.getTransactions[2]]: (state) => ({
    ...state,
    payments: [],
    totalCount: 0,
    isTransactionsDataLoading: false,
  }),
  [ActionTypes.getCategoryFilters[1]]: (state, salesCategories) => ({
    ...state,
    salesCategories,
  }),
}

export default generateReducer(transactionsInitialState, reductions)
