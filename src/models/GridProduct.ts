import {
  AmazonCustomerAccountClear,
  AmazonProductInfo,
  AmazonProductOffer,
  AmazonProductStock,
  IOptimization,
  IOptimizationTemplate,
  ProductInfo,
  ProductSetting,
} from "interfaces/GridInterfaces"
import IAmazonCustomerAccount from "interfaces/iAmazonCustomerAccount"
import ICustomerAccount from "interfaces/iCustomerAccount"
import { mainStateSelector } from "selectors/mainStateSelectors"

export default class GridProduct implements ProductInfo {
  amazonCustomerAccountClear: AmazonCustomerAccountClear
  id: number
  order_id: number | string
  sku: string
  title: string
  asin: string
  date_inserted: string
  date_updated: string
  amazon_customer_account_id: number
  seller_id: number | string
  deleted: number
  image: string
  group?: string | undefined
  type?: string | undefined
  product_group_id: number
  optimization_template_id?: string | undefined
  optimization_id: number
  comments: string
  min_shipping_hours?: string | undefined
  max_shipping_hours?: string | undefined
  optimization_active: number
  current_profit: string
  hash?: string | undefined
  prices_hash?: string | undefined
  stock_type: string
  zero_price: string
  fulfilment_fee: string
  fulfilment_non_pan_eu_fee: string
  other_fee: string
  vendor_price: string
  order_fee: string
  tax_amount: string
  min_price: string | number
  max_price: string | number
  condition: string
  private _min_price_type: string
  min_price_auto_type: string
  min_price_auto_value: string
  max_price_type: string
  max_price_auto_type: string
  max_price_auto_value: string
  used: number
  min_price_auto_field: string
  max_price_auto_field: string
  marketplace_id: string
  local_fulfilment_fee_updated: number
  shipment_tax_amount: string
  date_last_merchant_listing: string
  use_strategy_logger: number
  last_ping_time: number
  stand_alone_price: string
  old_optimization_active: number
  use_sale_price: number
  other_fee_max_fee?: number
  recommended_retail_price: string
  amazonProductOffer: AmazonProductOffer
  amazonProductStock: AmazonProductStock
  optimization: IOptimization
  optimizationTemplate: IOptimizationTemplate
  amazonProductInfo: AmazonProductInfo | null
  productGroup: any
  productSetting: ProductSetting
  percentFromVendorPrice: string
  is_enabled_sync_with_repricer: boolean
  product_asin?: string

  constructor(object: ProductInfo) {
    this.seller_id = object.seller_id
    this.amazon_customer_account_id = object.amazon_customer_account_id
    this.amazonProductInfo = object.amazonProductInfo
    this.amazonProductOffer = object.amazonProductOffer
    this.amazonProductStock = object.amazonProductStock
    this.asin = object.asin
    this.comments = object.comments
    this.condition = object.condition
    this.current_profit = object.current_profit
    this.date_inserted = object.date_inserted
    this.date_last_merchant_listing = object.date_last_merchant_listing
    this.date_updated = object.date_updated
    this.deleted = object.deleted
    this.fulfilment_fee = object.fulfilment_fee
    this.fulfilment_non_pan_eu_fee = object.fulfilment_non_pan_eu_fee
    this.id = object.id
    this.image = object.image
    this.last_ping_time = object.last_ping_time
    this.local_fulfilment_fee_updated = object.local_fulfilment_fee_updated
    this.marketplace_id = object.marketplace_id
    this.max_price = object.max_price
    this.max_price_auto_field = object.max_price_auto_field
    this.max_price_auto_type = object.max_price_auto_type
    this.max_price_auto_value = object.max_price_auto_value
    this.max_price_type = object.max_price_type
    this.min_price = object.min_price
    this.min_price_auto_field = object.min_price_auto_field
    this.min_price_auto_type = object.min_price_auto_type
    this.min_price_auto_value = object.min_price_auto_value
    this._min_price_type = object.min_price_type
    this.old_optimization_active = object.old_optimization_active
    this.optimization = object.optimization
    this.optimizationTemplate = object.optimizationTemplate
    this.optimization_active = object.optimization_active
    this.optimization_id = object.optimization_id
    this.order_fee = object.order_fee
    this.other_fee = object.other_fee
    this.productGroup = object.productGroup
    this.productSetting = object.productSetting
    this.product_group_id = object.product_group_id
    this.recommended_retail_price = object.recommended_retail_price
    this.shipment_tax_amount = object.shipment_tax_amount
    this.sku = object.sku
    this.stand_alone_price = object.stand_alone_price
    this.stock_type = object.stock_type
    this.tax_amount = object.tax_amount
    this.title = object.title
    this.use_sale_price = object.use_sale_price
    this.use_strategy_logger = object.use_strategy_logger
    this.used = object.used
    this.vendor_price = object.vendor_price
    this.zero_price = object.zero_price
    this.amazonCustomerAccountClear = object.amazonCustomerAccountClear
    this.other_fee_max_fee = object.other_fee_max_fee
    this.percentFromVendorPrice = object.percentFromVendorPrice
    this.is_enabled_sync_with_repricer = object.is_enabled_sync_with_repricer
    this.product_asin = object.product_asin
    this.order_id = object.order_id
  }

  /**
   * Get Order Last Count 30
   */
  getOrderLastCount30 = (): number => {
    const info = this.getAmazonProductInfo()
    if (info !== null) {
      return info.order_count_last_30
    }
    return 0
  }

  /**
   * Get Amazon Product Info
   */
  private getAmazonProductInfo = (): AmazonProductInfo | null => {
    return this.amazonProductInfo
  }

  /**
   *  Get Order Last Date
   */
  getOrderLastDate = (): string | false => {
    const info = this.getAmazonProductInfo()
    if (info !== null) {
      return info.order_last_date
    }
    return false
  }

  /**
   * Get Sales Last Rank One
   */
  getSalesRankOne = (): number => {
    const info = this.getAmazonProductInfo()
    if (info !== null) {
      return info.sales_rank_1
    }
    return 0
  }

  /**
   * Get Sales Last Rank Two
   */
  getSalesRankTwo = (): number => {
    const info = this.getAmazonProductInfo()
    if (info !== null) {
      return info.sales_rank_2
    }
    return 0
  }

  /**
   * Get Product Listing Price
   */
  getOfferListingPrice = (): string => {
    return this.amazonProductOffer.listing_price
  }

  /**
   * Use Sale Price
   */
  useSalePrice = (): number => {
    return this.use_sale_price
  }

  /**
   * Get Standard Price
   */
  getStandardPrice = (): string => {
    return this.amazonProductOffer.standard_price
  }

  isPrime = (): boolean | number => {
    return this.amazonProductOffer.is_prime
  }

  isNationalPrime = (): boolean | number => {
    return this.amazonProductOffer.is_national_prime
  }

  getCompetitivePriceThreshold = (): string => {
    return this.amazonProductOffer.competitive_price_threshold
  }

  isFeaturedMerchant = (): boolean | number => {
    return this.amazonProductOffer.is_featured_merchant
  }

  getMinPrice = (): number => {
    return parseFloat(this.getMinPriceString())
  }

  getMaxPrice = (): number => {
    return parseFloat(this.getMaxPriceString())
  }

  getOfferShippingPrice = (): number => {
    const globalShipping = this.getGlobalShipping()
    if (globalShipping !== null) {
      return parseFloat(globalShipping)
    }
    return this.amazonProductOffer && this.amazonProductOffer.shipping_price
      ? parseFloat(this.amazonProductOffer.shipping_price)
      : 0
  }

  get min_price_type(): string {
    return this._min_price_type
  }

  isMinPriceAuto = (): boolean => {
    return this.min_price_type === "AUTO"
  }

  isMaxPriceAuto = (): boolean => {
    return this.max_price_type === "AUTO"
  }

  getVendorPrice = (): number => {
    return parseFloat(this.vendor_price)
  }

  getOrderFee = (): number => {
    return parseFloat(this.order_fee)
  }

  getTaxAmount = (): number => {
    return parseFloat(this.tax_amount)
  }

  getCurrentProfit = (): number => {
    return parseFloat(this.current_profit)
  }
  getPercentFromVendorPrice = (): number => {
    return parseFloat(this.percentFromVendorPrice)
  }
  getBuyBoxCountWinners = (): number => {
    return this.amazonProductOffer.buy_box_count_winners
  }

  getBuyBoxWinnerPrices = (): string => {
    return this.amazonProductOffer.buy_box_winner_prices
  }

  getCountBeatItem = (): number => {
    return this.amazonProductOffer.count_beat_item
  }

  getMinLandedPrice = (): number => {
    return parseFloat(this.amazonProductOffer.min_landed_price)
  }

  getBuyBoxPrice = (): number => {
    return parseFloat(this.amazonProductOffer.buy_box_price)
  }

  getBuyBoxShippingPrice = (): number => {
    return parseFloat(this.amazonProductOffer.buy_box_shipping_price)
  }

  getZeroPrice = (): number => {
    return parseFloat(this.zero_price)
  }

  /**
   * Get Global Shipping
   */
  getGlobalShipping = (): string | null => {
    if (
      this.amazonCustomerAccountClear &&
      this.amazonCustomerAccountClear.ignore_amazon_shipping === 1
    ) {
      return this.amazonCustomerAccountClear.global_shipping
    }
    return null
  }

  getImageUrl = (): string => {
    const sellerId =
      this.amazonCustomerAccountClear?.sellerId || this.seller_id || ""
    const asin = this.asin || this.product_asin || ""
    return `https://images-na.ssl-images-amazon.com/images/P/${asin}.01-${sellerId}.MAIN.jpg`
  }

  /**
   * Get Amazon customer account info
   * @param accounts
   */
  getAmazonCustomerInfo(
    accounts: IAmazonCustomerAccount[],
  ): { sellerId?: string; customerAccountTitle?: string } | undefined {
    const amazonCustomerAccount = accounts.find(
      (account) => account.sellerId === this.seller_id,
    )

    if (amazonCustomerAccount) {
      return {
        sellerId: amazonCustomerAccount.sellerId,
        customerAccountTitle: amazonCustomerAccount.customerAccount.title,
      }
    }

    return undefined
  }

  /**
   * Get Amazon customer account - customer account title string
   * @param accounts
   */
  getAmazonCustomerSellerTitle = (
    accounts: IAmazonCustomerAccount[],
  ): string => {
    const info = this.getAmazonCustomerInfo(accounts)
    if (info) {
      return `${info.sellerId} (${info.customerAccountTitle})`
    }
    return ""
  }

  hasComments(): boolean {
    return !!(this.comments && this.comments.trim().length > 0)
  }

  getComments(): string[] {
    return this.hasComments() ? this.comments.split(/\n+/) : []
  }

  priceChangesLink = (): string | null => this.getPriceLink("log")

  priceNotificationsLink = (): string | null => this.getPriceLink("notify")

  private getPriceLink = (urlType: string): string | null => {
    const customer: ICustomerAccount = mainStateSelector()?.customer?.customer

    const r = urlType === "notify" ? "price-changes%2Fmongo-items" : "price-log"
    if (customer.id) {
      return `https://service.sl.local/index.php?r=${r}&id=${this.id}&customerId=${customer.id}`
    } else {
      console.log("no customer id found")
    }

    return null
  }

  getMinMaxPriceFee = (priceType: string): string | null => {
    const number = Math.random() * 10
    if (number > 5) {
      return `${number.toFixed(2)}`
    }
    return null
  }

  private getMinPriceString = (): string => {
    return `${this.min_price}`.length > 0 ? `${this.min_price}` : "0"
  }

  private getMaxPriceString = (): string => {
    return `${this.max_price}`.length > 0 ? `${this.max_price}` : "0"
  }

  getFulfilmentNonPanEuFee = (): number => {
    return parseFloat(this.fulfilment_non_pan_eu_fee)
  }

  getOtherFeeMaxFee = (): number => {
    return this.other_fee_max_fee !== undefined ? this.other_fee_max_fee : 0
  }

  getId = (): number => {
    return this.id
  }
}
