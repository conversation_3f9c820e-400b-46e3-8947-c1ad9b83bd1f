/// <reference path="./gridActions.d.ts"/>
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import api from "api"

import generateActions from "utils/generateActions"
import { ValidationErrorToFormik } from "utils/objectHelpers.ts"
import { checkIsArray } from "utils/arrayHelpers"

import AmazonMarketplace from "models/AmazonMarketplace"

import getMainApp from "components/MainAppProvider"

import {
  customerIdSelector,
  amazonMarketplacesSelector,
} from "selectors/mainStateSelectors"

const PRODUCT_PREFIX = "products"

const {
  productsService: { getProducts, updateProduct },
} = api

export const types = {
  setGridData: `${PRODUCT_PREFIX}/set_grid_data`,
  selectUnselectProduct: `${PRODUCT_PREFIX}/select_product`,
  selectUnselectProductRange: `${PRODUCT_PREFIX}/select_product_range`,
  changeSearchOptions: `${PRODUCT_PREFIX}/change_search_options`,
  pushUrl: [
    `${PRODUCT_PREFIX}/push_product_url`,
    `${PRODUCT_PREFIX}/push_product_url`,
    `${PRODUCT_PREFIX}/push_product_url`,
  ],
  getProducts: [
    `${PRODUCT_PREFIX}/get_products`,
    `${PRODUCT_PREFIX}/get_success`,
    `${PRODUCT_PREFIX}/get_failure`,
  ],
  init: [
    `${PRODUCT_PREFIX}/initialization`,
    `${PRODUCT_PREFIX}/initialization_success`,
    `${PRODUCT_PREFIX}/initialization_failure`,
  ],
  getProductCostCategories: [
    `${PRODUCT_PREFIX}/get_product_cost_categories_request`,
    `${PRODUCT_PREFIX}/get_product_cost_categories_success`,
    `${PRODUCT_PREFIX}/get_product_cost_categories_failure`,
  ],
  selectAll: `${PRODUCT_PREFIX}/selectAll`,
  selectTotalCount: `${PRODUCT_PREFIX}/selectTotalCount`,
  useSelectedList: `${PRODUCT_PREFIX}/use_selected_list`,
  showEditFieldForm: `${PRODUCT_PREFIX}/show_edit_field_form`,
  onUpdateProduct: [
    `${PRODUCT_PREFIX}/start_updating_product`,
    `${PRODUCT_PREFIX}/success_updating_product`,
    `${PRODUCT_PREFIX}/failure_updating_product`,
  ],
  updateGridRow: `${PRODUCT_PREFIX}/update_grid_row`,
}

let actions

const definitions = {
  changeSearchOptions: (searchOptions) => searchOptions,
  setGridData: (params) => params,
  getProducts: (params = {}, dispatch, getState) => ({
    callApi: () => {
      const state = getState()
      const customerId = customerIdSelector(state)

      if (params.useSelectedList && params.params !== undefined) {
        dispatch({
          type: types.useSelectedList,
          payload: { useSelectedList: params.useSelectedList },
        })
        params = params.params || {}
      }

      dispatch({
        type: types.changeSearchOptions,
        payload: params,
      })

      return getProducts({ ...params, customerId })
    },
  }),
  pushUrl: (params, dispatch) => ({
    async callApi() {
      dispatch({
        type: types.changeSearchOptions,
        payload: params,
      })

      const { actions: mainAppActions } = getMainApp()
      mainAppActions?.pushUrl?.(getUrlSearchParamsString({ params }))

      return {
        data: params,
      }
    },
  }),
  selectUnselectProduct: (product) => product,
  selectUnselectProductRange: (products) => products,
  selectAll: (selected) => ({ selected }),
  selectTotalCount: (selected) => ({ selected }),
  showEditFieldForm: ({ formType, isVisible, productId }) => ({
    formType,
    isVisible,
    productId,
  }),
  onUpdateProduct: (
    { productId, values, customerId },
    callback = () => {},
    errorCallback = null,
    dispatch,
  ) => ({
    callApi: async () => {
      const data = await updateProduct({
        productId,
        values,
        customerId,
      })
      return { ...data }
    },
    successCallback: (gridObject) => {
      if (callback) callback()
      dispatch(actions.updateGridRow(gridObject))
    },
    failureCallback: (payload) => {
      if (errorCallback) {
        if (checkIsArray(payload)) {
          errorCallback(ValidationErrorToFormik(payload))
        } else {
          errorCallback(payload)
        }
      }
    },
  }),
  updateGridRow: (gridObject) => gridObject,
  init: (params = {}, dispatch, getState) => ({
    callApi: async () => {
      const state = getState()
      const { amazonMarketplaces } = amazonMarketplacesSelector(state)

      const AmazonCustomerAccountMarketplaces = amazonMarketplaces.map(
        (marketplace) => new AmazonMarketplace(marketplace),
      )

      return {
        data: {
          marketPlaces: AmazonCustomerAccountMarketplaces,
        },
      }
    },
    doNotShowSpin: true,
  }),
}

actions = generateActions(definitions, types)

export default actions
