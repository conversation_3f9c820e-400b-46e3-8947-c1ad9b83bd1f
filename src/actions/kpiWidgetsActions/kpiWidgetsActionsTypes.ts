import { ActionAbstractDefinitionType, ActionsTypes } from "actions/types"

export type KPIWidgetsActionsAsyncTypeNames = never

export type KPIWidgetsActionsSyncTypeNames =
  | "setIsInitiated"
  | "setKPIWidgetsSettings"
  | "setDateRangeByIndex"

export type KPIWidgetsTypes = ActionsTypes<
  KPIWidgetsActionsAsyncTypeNames,
  KPIWidgetsActionsSyncTypeNames
>

export type KPIWidgetsDefinitions = {
  setIsInitiated: ActionAbstractDefinitionType
  setKPIWidgetsSettings: ActionAbstractDefinitionType
  setDateRangeByIndex: ActionAbstractDefinitionType
}
