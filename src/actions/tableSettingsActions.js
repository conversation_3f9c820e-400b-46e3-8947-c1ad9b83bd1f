import api from "api"
import generateActions from "utils/generateActions"

import { DEFAULT_PAGE_SIZE } from "constants/grid"

const {
  tableSettingsService: { get, save, update, saveDefault, updateDefault },
} = api

const tableSettingsPrefix = `tableSettings`

export const types = {
  display: `${tableSettingsPrefix}/display`,
  setIsResizingMode: `${tableSettingsPrefix}/is_resizing_mode`,
  updateTableSettings: `${tableSettingsPrefix}/update_table_settings`,
  get: [
    `${tableSettingsPrefix}/get_request`,
    `${tableSettingsPrefix}/get_success`,
    `${tableSettingsPrefix}/get_failure`,
  ],
  save: [
    `${tableSettingsPrefix}/save_request`,
    `${tableSettingsPrefix}/save_success`,
    `${tableSettingsPrefix}/save_failure`,
  ],
  defaultSave: [
    `${tableSettingsPrefix}/defaultSave_request`,
    `${tableSettingsPrefix}/defaultSave_success`,
    `${tableSettingsPrefix}/defaultSave_failure`,
  ],
  footerSize: `${tableSettingsPrefix}/footer_size`,
}

const definitions = {
  display: (key, value = false) => ({
    selectedTableSettings: key,
    visible: value,
  }),
  setIsResizingMode: (isResizingMode = true) => ({ isResizingMode }),
  updateTableSettings: (newSettings, dispatch, getState) => {
    const {
      tableSettings: { selectedTableSettings, settings },
    } = getState()

    return {
      ...settings,
      [selectedTableSettings]: {
        ...settings[selectedTableSettings],
        settings: newSettings,
      },
    }
  },
  get: (key, successCallback, defaultKey) => ({
    callApi: async () => {
      let settings = (await get(key)).data[0]
      if (
        typeof defaultKey === "string" &&
        ((settings && !settings.value.includes("width")) || !settings)
      ) {
        const defaultSettings = (await get(defaultKey, 1)).data[0]
        if (defaultSettings && defaultSettings.value) {
          settings = defaultSettings
        }
      }

      if (!settings) {
        settings = {
          key,
          value: "{}",
        }
      }

      let asSettings
      let pageSize = DEFAULT_PAGE_SIZE
      if (Array.isArray(settings)) {
        asSettings = settings
      } else {
        const value = JSON.parse(settings.value)
        asSettings = value.settings
        pageSize = value.pageSize
      }

      return {
        data: {
          id: settings.id,
          key,
          settings: asSettings,
          pageSize,
        },
      }
    },
    successCallback,
  }),
  footerSize: (bounds) => ({ bounds }),
  save: ({ settings, pageSize }, callback, dispatch, getState) => ({
    callApi: async () => {
      const {
        tableSettings: { selectedTableSettings },
      } = getState()

      let [setting] = (await get(selectedTableSettings)).data

      let payload
      const canUpdateTableSetting = setting && setting.id

      if (canUpdateTableSetting) {
        payload = (
          await update(setting.id, JSON.stringify({ settings, pageSize }))
        ).data
      } else {
        payload = (
          await save(
            selectedTableSettings,
            JSON.stringify({ settings, pageSize }),
          )
        ).data
      }

      const { key, value } = payload

      const settingsObject = JSON.parse(value)

      return {
        data: {
          id: payload.id,
          key,
          pageSize: DEFAULT_PAGE_SIZE,
          settings: [],
          ...settingsObject,
        },
      }
    },
    callback,
    doNotShowSpin: true,
  }),
  defaultSave: (
    { settings, pageSize },
    language,
    callback,
    dispatch,
    getState,
  ) => ({
    callApi: async () => {
      const {
        tableSettings: { selectedTableSettings },
      } = getState()
      language = language[0].toUpperCase() + language.slice(1)

      let [setting] = (
        await get(`${selectedTableSettings}Default${language}`, 1)
      ).data

      let payload

      if (setting && setting.id) {
        payload = (
          await updateDefault(
            setting.id,
            `${selectedTableSettings}Default${language}`,
            JSON.stringify({ settings, pageSize }),
          )
        ).data
      } else {
        payload = (
          await saveDefault(
            `${selectedTableSettings}Default${language}`,
            JSON.stringify({ settings, pageSize }),
          )
        ).data
      }

      const { key, value } = payload

      const settingsObject = JSON.parse(value)

      return {
        data: {
          id: payload.id,
          key,
          pageSize: DEFAULT_PAGE_SIZE,
          settings: [],
          ...settingsObject,
        },
      }
    },
  }),
}

export default generateActions(definitions, types)
