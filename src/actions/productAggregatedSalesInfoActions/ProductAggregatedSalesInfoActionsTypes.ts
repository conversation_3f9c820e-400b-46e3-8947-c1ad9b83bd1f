import type { Dispatch } from "redux"

import type { ActionAsyncDefinitionType, ActionsTypes } from "actions/types"

import { AsyncStatus } from "types"
import { ProductAggregatedSalesInfoTableGroupBy } from "types/ProductAggregatedSalesInfoTableGroupBy"
import type { GetProductAggregatedSalesInfosRequestParams } from "types/RequestParams/GetProductAggregatedSalesInfosRequestParams"
import type { ProductAggregatedSalesInfoNormalized } from "types/store/ProductAggregatedSalesInfo"
import type { RootState } from "types/store/store"
import type {
  TableSettingsActionDefinitions,
  TableSettingsActionTypes,
} from "types/TableSettings"

export type ProductAggregatedSalesInfoActionsAsyncTypeNames =
  | "getProductAggregatedSalesInfoTableData"
  | "expandRow"

export type ProductAggregatedSalesInfoActionsSyncTypeNames =
  | "changeExpandedRowStatus"
  | "collapseRow"

export type ProductAggregatedSalesInfoTypes = ActionsTypes<
  ProductAggregatedSalesInfoActionsAsyncTypeNames,
  ProductAggregatedSalesInfoActionsSyncTypeNames
> &
  TableSettingsActionTypes

export type GetProductAggregatedSalesInfoTableDataParams = {
  expandedRows: Record<string, number>
  groupBy?: ProductAggregatedSalesInfoTableGroupBy
  requestParams: Omit<GetProductAggregatedSalesInfosRequestParams, "offer_type">
}

type ChangeExpandedRowStatusParams = {
  id: string
  status: AsyncStatus
  error: null | any
}

type ChangeExpandedRowStatusReturn = {
  expandedRowStatuses: Record<string, AsyncStatus>
  expandedRowErrors: Record<string, any | null>
}

export type ProductAggregatedSalesInfoDefinitions = {
  getProductAggregatedSalesInfoTableData: ActionAsyncDefinitionType<GetProductAggregatedSalesInfoTableDataParams>
  changeExpandedRowStatus: (
    params: ChangeExpandedRowStatusParams,
    dispatch?: Dispatch,
    getState?: () => RootState,
  ) => ChangeExpandedRowStatusReturn
  expandRow: ActionAsyncDefinitionType<{
    id: string
    page: number
    requestParams: GetProductAggregatedSalesInfosRequestParams
  }>
  collapseRow: (
    id: string,
    dispatch?: Dispatch,
    getState?: () => RootState,
  ) => Array<ProductAggregatedSalesInfoNormalized>
} & TableSettingsActionDefinitions
