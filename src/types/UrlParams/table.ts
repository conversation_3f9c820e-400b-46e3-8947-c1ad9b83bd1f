import { UrlParams } from "./common"

export type TableCommonUrlParams = {
  page: number
  pageSize: number
  sort: string
}

export type TableUrlParams<Table extends UrlParams> = TableCommonUrlParams &
  Table

export type TableUrlParamsWithPrefix<
  Table extends UrlParams,
  Prefix extends string,
> = {
  [K in keyof TableCommonUrlParams as `${Prefix}-${K}`]: TableCommonUrlParams[K]
} & {
  [K in keyof Table & string as `${Prefix}-${K}`]: Table[K]
}
