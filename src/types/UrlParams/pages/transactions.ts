import { CustomerIdUrlParams } from "../common"
import { CurrencyUrlParams } from "../currency"
import { DateRangeUrlParams } from "../dates"
import { GridUrlParams } from "../grid"
import { MarketplaceIdUrlParams, MarketplacesUrlParams } from "../marketplaces"
import { GroupAccountUrlParams } from "../seller"

import { TransactionKPIType } from "types/Transaction"

export type TransactionsLevelsUrlParams = {
  transaction_level?: string
}

export type TransactionDateUrlParams = {
  transaction_date?: string
}

export type TransactionsSalesCategoryDepthLevel1UrlParams = {
  sales_category_depth_1?: string[]
  sales_category_depth_2?: string[]
}

export type TransactionsGridParams = GridUrlParams &
  MarketplaceIdUrlParams &
  TransactionsLevelsUrlParams &
  TransactionDateUrlParams &
  TransactionsSalesCategoryDepthLevel1UrlParams & {
    amazon_order_id?: string
    seller_id?: string
    sku?: string
    asin?: string
    title?: string
    product_id?: string
    product_brand?: string
    product_type?: string
    product_manufacturer?: string
    condition?: string
    stock_type?: string
    offer_type?: string
    amount?: string
    posted_date?: string
    transaction_type?: string[]
  }

export type TransactionsKPITypeUrlParams = {
  kpi_type?: TransactionKPIType[]
}

export type TransactionsUrlParams = Pick<CustomerIdUrlParams, "customerID"> &
  GroupAccountUrlParams &
  DateRangeUrlParams &
  MarketplacesUrlParams &
  Pick<CurrencyUrlParams, "currency_id"> &
  TransactionsGridParams &
  TransactionsKPITypeUrlParams
