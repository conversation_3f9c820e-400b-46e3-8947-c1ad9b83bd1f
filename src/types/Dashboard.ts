import {
  DASHBOARD_DEFAULT_VALUES_STATUS,
  DASH<PERSON>ARD_USER_SESSION_BY_TAB,
} from "constants/dashboard"

export type DashboardSessionUrlParams = {
  search: string
  timestamp?: string
}

export type DashboardUserSessionByTabs =
  typeof DASHBOARD_USER_SESSION_BY_TAB[keyof typeof DASHBOARD_USER_SESSION_BY_TAB]

export type DefaultValuesStatus =
  typeof DASHBOARD_DEFAULT_VALUES_STATUS[keyof typeof DASHBOARD_DEFAULT_VALUES_STATUS]
