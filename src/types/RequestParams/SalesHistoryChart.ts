import { BooleanRequestParam, JSONRequestParam } from "./global"

import { CategoryStrategy, PeriodType } from "types/UrlParams"

export type SalesHistoryChartRequestParams = {
  dateStart: string
  dateEnd: string
  customerId?: string
  isTransactionDateMode?: BooleanRequestParam
  marketplaceSellerIds?: JSONRequestParam
  sellerId?: string
  marketplaceId?: string
  currencyId?: string
  asin?: string
  ean?: string
  upc?: string
  isbn?: string
  parentAsin?: string
  brand?: string
  productType?: string
  stockType?: string
  manufacturer?: string
  adultProduct?: BooleanRequestParam
  sellerSku?: string
  mainImage?: string
  model?: string
  salesCategoryId?: string
  periodType: PeriodType
  offerType?: string
  tagId?: string
  sales_category_strategy?: CategoryStrategy
  maxDepth?: number
}
