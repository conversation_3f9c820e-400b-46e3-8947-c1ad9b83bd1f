import { SALES_CATEGORY_STRATEGIES } from "constants/general"

import { BooleanRequestParam, JSONRequestParam } from "./global"

export type KPIWidgetRequestParams = {
  dateStart: string
  dateEnd: string
  customerId?: string
  isTransactionDateMode?: BooleanRequestParam
  marketplaceSellerIds?: JSONRequestParam
  sellerId?: string
  marketplaceId?: string
  currencyId?: string
  asin?: string
  ean?: string
  upc?: string
  isbn?: string
  parentAsin?: string
  brand?: string
  productType?: string
  stockType?: string
  manufacturer?: string
  adultProduct?: string
  sellerSku?: string
  mainImage?: string
  model?: string
  offerType?: string
  tagId?: string
  sales_category_strategy?: keyof typeof SALES_CATEGORY_STRATEGIES
}
