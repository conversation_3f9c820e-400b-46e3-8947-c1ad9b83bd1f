export type ProductFiltersRequestParams = {
  seller_id?: string
  marketplace_id?: string
  date_start?: string
  date_end?: string
  currency_code?: string
  title?: string
  asin?: string
  sku?: string
  ean?: string
  upc?: string
  isbn?: string
  brand?: string
  product_type?: string
  stock_type?: string
  manufacturer?: string
  parent_asin?: string
  adult_product?: "1" | "0"
  search?: string
  age_range?: string
  buying_price?: string
  is_enabled_sync_with_repricer?: "1" | "2"
  marketplaceSellerIds?: string
  other_fees?: string
  repricer_id?: "1" | "2"
  shipping_cost?: string
  vat?: string
  tag_id?: string
}
