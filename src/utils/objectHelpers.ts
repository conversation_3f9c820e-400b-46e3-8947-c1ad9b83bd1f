import { getObjectEntries, getObjectKeys } from "@develop/fe-library/dist/utils"
import pickBy from "lodash/pickBy"

import { checkIsString } from "./validationHelper"

interface NumberedObject {
  [key: string]: number
}

interface FormikErrorsObject {
  [key: string]: string
}

interface ErrorLineObject {
  field: string
  message: string
}

type ObjectTypes<T> = { [key: string]: T }

const hasOnlyNumber = (value: string): boolean => {
  const rg = /^[\d,.]+$/

  return rg.test(value)
}

/**
 * Replace Commas
 * @param value {String}
 * @returns {*}
 */
export const ReplaceCommas = (value: string): string => value.replace(/,/g, ".")

/**
 * Replace object string number by numbers
 * @param object
 * @returns {{}}
 * @constructor
 */
export const StringNumberToNumber = (object: Record<string, unknown>): object =>
  getObjectEntries(object).reduce((final: NumberedObject, [key, value]) => {
    if (hasOnlyNumber(value)) {
      final[key] = parseFloat(ReplaceCommas(value))
    } else {
      final[key] = value
    }

    return final
  }, {})

/**
 * Map API response with validation error messages to Formik Object
 * @param errors
 * @return {Object}
 * @constructor
 */
export const ValidationErrorToFormik = (errors: []): object =>
  errors.reduce((acc: FormikErrorsObject, line: ErrorLineObject) => {
    acc[line.field] = line.message

    return acc
  }, {})

/**
 * Returns a new object with all non-null values from the original object, excluding any keys specified in the excludedKeys array.
 * @param object - The original object.
 * @param excludedKeys - An array of keys to exclude from the new object.
 * @returns A new object with all non-null values from the original object, excluding any keys specified in the excludedKeys array.
 */
export const getClearObject = <
  T extends Record<string, unknown>,
  K extends keyof T,
>({
  object,
  excludedKeys,
}: {
  object: T
  excludedKeys?: K[]
}): Omit<T, K> => {
  return Object.fromEntries(
    getObjectEntries(object).filter(
      ([key, value]) => value && !excludedKeys?.includes(key as K),
    ),
  ) as Omit<T, K>
}

export const removeNullAndUndefined = <T extends object>(
  initialObject: T,
): Partial<T> =>
  pickBy(initialObject, (v) => v !== undefined && v !== null && v !== "")

export const isNonEmptyObject = <T>(object: ObjectTypes<T>): boolean => {
  return object && !!getObjectKeys(object).length
}

export const isObjectDataEmpty = <T>(object: ObjectTypes<T>): boolean => {
  const isNumber =
    typeof object?.data === "number" || object?.data instanceof Number
  const isString = checkIsString(object?.data) || object?.data instanceof String

  if (!object || !object?.data) {
    return true
  }

  if (isString || isNumber) {
    return false
  }

  if (object.data)
    if (Array.isArray(object.data)) {
      return !object?.data?.length
    }

  return !getObjectKeys(object.data).length
}

/**
 * Check if the value is an object
 * @param value - The value to check
 *
 * @returns True if the value is an object, false otherwise
 */
export const checkIsObject = (value: unknown): value is object => {
  return (
    typeof value === "object" &&
    value !== null &&
    !Array.isArray(value) &&
    Object.getPrototypeOf(value) === Object.prototype
  )
}
