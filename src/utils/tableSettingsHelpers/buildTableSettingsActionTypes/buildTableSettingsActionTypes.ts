import { withActionPrefix } from "actions/utils"

import type { TableSettingsActionTypes } from "types/TableSettings"

export const buildTableSettingsActionTypes = (
  prefix: string,
): TableSettingsActionTypes => {
  const createActionType = withActionPrefix(prefix)

  // Use of ActionsTypes causes a strange error when not including sync types.
  // TODO: check and fix.
  // @ts-expect-error
  return {
    getTableSettings: createActionType("getTableSettings", true),
    updateTableSettings: createActionType("updateTableSettings", true),
    updateDefaultTableSettings: createActionType(
      "updateDefaultTableSettings",
      true,
    ),
  }
}
