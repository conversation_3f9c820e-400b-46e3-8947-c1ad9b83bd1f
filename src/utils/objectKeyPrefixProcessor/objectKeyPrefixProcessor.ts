import { getObjectKeys } from "@develop/fe-library/dist/utils"

import {
  ObjectKeyPrefixProcessor,
  RemoveValuesWithPrefixProcessor,
} from "./ObjectKeyPrefixProcessorTypes"

export const addPrefixToObjectKeys: ObjectKeyPrefixProcessor = ({
  object,
  prefix,
  whiteList = [],
}) => {
  if (!prefix) {
    return object
  }

  // Target only keys that are present in the whiteList
  return whiteList.reduce((acc, key) => {
    if (!object[key]) {
      return acc
    }

    const newKey = `${prefix}-${key}`

    return {
      ...acc,
      [newKey]: object[key],
    }
  }, {})
}

export const removePrefixFromObjectKeys: ObjectKeyPrefixProcessor = ({
  object,
  prefix,
  whiteList = [],
}) => {
  if (!prefix) {
    return object
  }

  const whiteListProcessed = whiteList.map((key) => `${prefix}-${key}`)

  return whiteListProcessed.reduce((acc, key) => {
    if (!object[key]) {
      return acc
    }

    const newKey = key.replace(`${prefix}-`, "")

    return {
      ...acc,
      [newKey]: object[key],
    }
  }, {})
}

export const removeValuesWithPrefixInKeys: RemoveValuesWithPrefixProcessor = ({
  object,
  prefix,
}) => {
  if (!prefix) {
    return object
  }

  return getObjectKeys(object).reduce((acc, key) => {
    const [foundPrefix] = key.split("-")

    if (foundPrefix === prefix) {
      return acc
    }

    return {
      ...acc,
      [key]: object[key],
    }
  }, {})
}
