export const XS = 'xs'
export const SM = 'sm'
export const MD = 'md'
export const LG = 'lg'
export const XL = 'xl'
export const XXL = 'XXL'

export const sizes = {
  [SM]: 575,
  [MD]: 768,
  [LG]: 1024,
  [XL]: 1366,
  [XXL]: 1600,
}

const order = {
  [XS]: 1,
  [SM]: 2,
  [MD]: 3,
  [LG]: 4,
  [XL]: 5,
  [XXL]: 6,
}

export const getBreakpoint = width => {
  if (width <= sizes[SM]) {
    return XS
  } else if (width <= sizes[MD]) {
    return SM
  } else if (width <= sizes[LG]) {
    return MD
  } else if (width <= sizes[XL]) {
    return LG
  } else if (width <= sizes[XXL]) {
    return XL
  }

  return XXL
}

export const biggerThan = (currentDimension, targetDimension) =>
  order[currentDimension] > order[targetDimension]

export const lowerThan = (currentDimension, targetDimension) =>
  order[currentDimension] < order[targetDimension]
