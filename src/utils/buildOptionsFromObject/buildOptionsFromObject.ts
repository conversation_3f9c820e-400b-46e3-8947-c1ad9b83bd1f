import { checkIsFunction } from "@develop/fe-library/dist/utils"

import l from "utils/intl"

import type { BuildOptionsFromObject } from "./BuildOptionsFromObjectTypes"

export const buildOptionsFromObject: BuildOptionsFromObject = ({
  object,
  shouldTranslateLabel,
  transformValue,
}) => {
  return Object.entries(object).map(([value, label]) => ({
    value: checkIsFunction(transformValue) ? transformValue(value) : value,
    label: shouldTranslateLabel ? l(label) : label,
  }))
}
