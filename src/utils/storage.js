/**
 * Get item from local or session storage
 * @param {string} key
 * @param {boolean} session
 *
 * @returns {string|null}
 */
export const getItem = (key, session) => {
  const storage = session ? sessionStorage : localStorage

  return storage.getItem(key)
}

/**
 * Set item in local or session storage
 * @param {string} key
 * @param {string | undefined} value
 * @param {boolean} isSessionStorage
 * @param {boolean} shouldDispatchEvent
 */
export const setItem = (
  key,
  value,
  isSessionStorage = false,
  shouldDispatchEvent = false,
) => {
  const storage = isSessionStorage ? sessionStorage : localStorage

  const oldValue = localStorage.getItem(key)

  if (value) {
    storage.setItem(key, value)
  } else {
    storage.removeItem(key)
  }

  if (!shouldDispatchEvent) {
    return
  }

  // Dispatch a custom storage event
  const storageEvent = new StorageEvent("storage", {
    key,
    newValue: value,
    oldValue,
    storageArea: storage,
  })

  window.dispatchEvent(storageEvent)
}
