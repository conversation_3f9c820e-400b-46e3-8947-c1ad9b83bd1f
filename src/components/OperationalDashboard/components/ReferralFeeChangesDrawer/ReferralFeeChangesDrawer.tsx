import React from "react"
import { ROUTES } from "@develop/fe-library/dist/routes"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import l from "utils/intl"

import { CommonDrawerWithSimpleTable } from "../../components"

import { useReferralFeeChangesDrawer } from "./hooks"

import { ReferralFeeChangesDrawerProps } from "./ReferralFeeChangesDrawerTypes"

export const ReferralFeeChangesDrawer = ({
  isOpen,
}: ReferralFeeChangesDrawerProps) => {
  const {
    dataReferralFeeChanges,
    isDataReferralFeeChangesLoading,
    totalCount,
    closeDrawerHandler,
    getDataCompletenessReferralFeeChangesHandler,
  } = useReferralFeeChangesDrawer()

  return (
    <CommonDrawerWithSimpleTable
      data={dataReferralFeeChanges}
      drawerTitle={l("Referral fee changes")}
      isLoading={isDataReferralFeeChangesLoading}
      isOpen={isOpen}
      totalCount={totalCount}
      redirectUrl={`${
        ROUTES.BAS_ROUTES.PATH_BAS_TRANSACTIONS
      }${getUrlSearchParamsString({
        params: {
          "sales_category_depth_2[]": "referral_fee_2-referral_fee_3",
        },
      })}`}
      onCloseDrawer={closeDrawerHandler}
      onGetData={getDataCompletenessReferralFeeChangesHandler}
    />
  )
}
