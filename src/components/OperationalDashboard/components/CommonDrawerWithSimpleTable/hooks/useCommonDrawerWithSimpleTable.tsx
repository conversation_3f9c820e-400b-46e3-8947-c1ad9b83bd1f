import React, { useCallback, useEffect, useMemo, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Box, EmptyImage, Skeleton, Typography } from "@develop/fe-library"

import { operationalDashboardActions } from "actions/operationalDashboardActions"

import { amazonMarketplacesSelector } from "selectors/mainStateSelectors"

import LinkView from "components/shared/Link"

import l from "utils/intl"
import { getAmazonProductLink } from "utils/links"
import ln from "utils/localeNumber"
import { checkIsNullOrUndefined } from "utils/validationHelper"

import { useCommonDrawerWithSimpleTableProps } from "./useCommonDrawerWithSimpleTableTypes"

const { clearOperationalDashboardDrawersData } = operationalDashboardActions

export const useCommonDrawerWithSimpleTable = ({
  isOpen,
  redirectUrl,
  onGetData,
}: useCommonDrawerWithSimpleTableProps) => {
  const [currentPage, setCurrentPage] = useState(1)

  const { amazonMarketplaces } = useSelector(amazonMarketplacesSelector)

  const dispatch = useDispatch()

  const buildProductLink = useCallback(
    (product: any): string => {
      const getProductMarketplace = (productMarketplaceId: string) =>
        amazonMarketplaces?.find(({ id }) => id === productMarketplaceId)

      const productMarketplace = getProductMarketplace(product.marketplace_id)

      return getAmazonProductLink(
        productMarketplace?.sales_channel,
        product.asin,
      )
    },
    [amazonMarketplaces],
  )

  const columns = useMemo(() => {
    return [
      {
        title: l("Image"),
        key: "productImage",
        dataIndex: "productImage",
        width: 64,
        hasPadding: false,
        renderCell: ({ value }) => (
          <EmptyImage height={54} url={value} width={54} />
        ),
      },
      {
        title: l("Title"),
        key: "product_title",
        dataIndex: "product_title",
        width: 220,
        minWidth: 220,
        hasEllipsis: true,
        renderLoadingCell: () => (
          <Box flexDirection="column" gap="s" justify="center" width="100%">
            <Skeleton isUnwrap count={3} height="1ch" />
          </Box>
        ),
      },
      {
        title: "SKU",
        key: "seller_sku",
        dataIndex: "seller_sku",
        width: 134,
        minWidth: 134,
        hasEllipsis: true,
        renderCell: ({ value }) => value ?? l("N/A"),
      },
      {
        title: "ASIN",
        key: "product_asin",
        dataIndex: "product_asin",
        width: 134,
        renderCell: ({ value, item }) => {
          if (checkIsNullOrUndefined(value)) {
            return l("N/A")
          }

          const productLink = buildProductLink({
            marketplace_id: item?.marketplace_id,
            asin: value,
          })

          if (!productLink) {
            return value
          }

          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={value}
              type="span"
              url={productLink}
              variant="textSmall"
            />
          )
        },
      },
      {
        title: l("Original value"),
        key: "prev_amount",
        dataIndex: "prev_amount",
        width: 134,
        renderCell: ({ value, item }) => {
          if (checkIsNullOrUndefined(value)) {
            return l("N/A")
          }

          const formattedValue: string = ln(value, 2, {
            currency: item.currency_id,
          })

          return (
            <Typography variant="--font-body-text-9">
              {formattedValue}
            </Typography>
          )
        },
      },
      {
        title: l("Current value"),
        key: "current_amount",
        dataIndex: "current_amount",
        width: 134,
        renderCell: ({ value, item }) => {
          if (checkIsNullOrUndefined(value)) {
            return l("N/A")
          }

          const formattedValue: string = ln(value, 2, {
            currency: item.currency_id,
          })

          return (
            <Typography variant="--font-body-text-9">
              {formattedValue}
            </Typography>
          )
        },
      },
      {
        title: "Action",
        key: "action",
        dataIndex: "action",
        width: 134,
        renderCell: () => {
          return (
            <LinkView
              internal={false}
              rel="noopener noreferrer"
              styleType="primary"
              target="_blank"
              text={l("See details")}
              type="span"
              url={redirectUrl}
              variant="textSmall"
            />
          )
        },
      },
    ]
  }, [buildProductLink, amazonMarketplaces])

  const changePageHandler = useCallback((page: number) => {
    setCurrentPage(page)
  }, [])

  useEffect(() => {
    if (isOpen) {
      onGetData(currentPage)
    } else {
      dispatch(clearOperationalDashboardDrawersData())
    }
  }, [currentPage, isOpen])

  return {
    columns,
    currentPage,
    changePageHandler,
  }
}
