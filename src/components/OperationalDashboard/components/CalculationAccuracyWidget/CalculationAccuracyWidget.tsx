import React from "react"
import { useSelector } from "react-redux"
import { Box, Spinner, Typography } from "@develop/fe-library"
import { checkIsNumber } from "@develop/fe-library/dist/utils"
import { <PERSON>, <PERSON>, <PERSON><PERSON>, ResponsiveContainer } from "recharts"

import {
  operationalDashboardFillPercentageWidgetHealthScoreLevelSelector,
  operationalDashboardIsDataCompletenessLoadingSelector,
} from "selectors/operationalDashboardSelectors/operationalDashboardSelectors"

import { GenericWidget } from "components/shared/Widget"

import l from "utils/intl"

import { CALCULATION_ACCURACY_WIDGET_COLORS } from "./constants"

export const CalculationAccuracyWidget = () => {
  const { fillPercentageWidgetHealthScoreLevel, data } = useSelector(
    operationalDashboardFillPercentageWidgetHealthScoreLevelSelector,
  )
  const isDataCompletenessLoading = useSelector(
    operationalDashboardIsDataCompletenessLoadingSelector,
  )

  return (
    <GenericWidget
      isCustomWrapper
      containerProps={{ height: "100%", minHeight: 326 }}
      content={
        <Box align="center" gap="s">
          <Typography variant="--font-body-text-2">
            {l("Calculation accuracy")}
          </Typography>
        </Box>
      }
    >
      <Box height="100%" justify="center" padding="l">
        <Box
          align="center"
          justify="center"
          minHeight={246}
          position="relative"
          width={270}
        >
          {isDataCompletenessLoading ? (
            <Spinner tip={l("Loading...")} />
          ) : (
            <Box display="block" height={246} position="relative" width={270}>
              <ResponsiveContainer height="100%" width="100%">
                <PieChart
                  margin={{
                    top: 0,
                    right: 0,
                    left: 0,
                    bottom: 0,
                  }}
                >
                  <Pie
                    cx="50%"
                    cy="55%"
                    data={data}
                    dataKey="value"
                    endAngle={-45}
                    innerRadius={110}
                    outerRadius={135}
                    startAngle={225}
                    stroke="none"
                  >
                    {data.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={CALCULATION_ACCURACY_WIDGET_COLORS[index]}
                      />
                    ))}
                  </Pie>
                </PieChart>
              </ResponsiveContainer>
              <Box
                align="center"
                flexDirection="column"
                left="50%"
                position="absolute"
                top="50%"
                transform="translate(-50%, -50%)"
              >
                {checkIsNumber(fillPercentageWidgetHealthScoreLevel) ? (
                  <>
                    <Typography variant="--font-headline-1">
                      {fillPercentageWidgetHealthScoreLevel}%
                    </Typography>
                    <Typography textAlign="center" variant="--font-body-text-2">
                      {l("Health score level")}
                    </Typography>
                  </>
                ) : (
                  <Typography
                    color="--color-text-second"
                    variant="--font-headline-2"
                  >
                    {l("No data")}
                  </Typography>
                )}
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </GenericWidget>
  )
}
