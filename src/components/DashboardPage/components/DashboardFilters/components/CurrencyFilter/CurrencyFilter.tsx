import React from "react"
import { Select } from "@develop/fe-library"

import { CurrencyOption } from "components/SegmentsSidebar/components/Filters/components/MainFilter/components"

import l from "utils/intl"

import { useCurrencyFilter } from "./hooks"

export const CurrencyFilter = () => {
  const {
    value,
    handleCurrencyChange,
    isDisabled,
    currencyOptions,
    prefixIcons,
  } = useCurrencyFilter()

  return (
    <Select
      hasSearch
      isFullWidth
      isGlobal
      hasLabelTooltip={false}
      isDisabled={isDisabled}
      label={l("Currency")}
      options={currencyOptions}
      prefixIcons={prefixIcons}
      renderOption={CurrencyOption}
      value={value}
      onChangeValue={handleCurrencyChange}
    />
  )
}
