import React from "react"
import { Box } from "@develop/fe-library"

import { TabContentContainerProps } from "./TabContentContainerTypes"

export const TabContentContainer = ({ children }: TabContentContainerProps) => {
  return (
    <Box
      display="block"
      mSM={{
        marginLeft: "calc(-1 * var(--margin-m))",
        marginRight: "calc(-1 * var(--margin-m))",
      }}
      mXL={{
        marginLeft: "calc(-1 * var(--margin-l))",
        marginRight: "calc(-1 * var(--margin-l))",
      }}
    >
      {children}
    </Box>
  )
}
