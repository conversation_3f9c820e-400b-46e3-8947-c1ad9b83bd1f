import React from "react"
import { InputMode, Typography } from "@develop/fe-library"

import { useDateRange, useUrlParams } from "hooks"

import { convertToLocalDate } from "utils/dateConverter"

import { DashboardFiltersParams } from "types"

export const DashboardTableWidgetsTitle = () => {
  const { INPUTMODE_LIST } = useDateRange()
  const { urlParams } = useUrlParams<DashboardFiltersParams>()

  const isDateRange: boolean = !!(
    urlParams.from &&
    urlParams.to &&
    urlParams.inputMode
  )

  if (!isDateRange) {
    return null
  }

  return (
    <>
      <Typography variant="--font-body-text-2">
        {INPUTMODE_LIST[urlParams.inputMode as InputMode]}:{" "}
        {convertToLocalDate(urlParams.from as string)}
        {" - "}
        {convertToLocalDate(urlParams.to as string)}
      </Typography>
    </>
  )
}
