import React, { useEffect, useMemo, useState } from "react"
import { useSelector } from "react-redux"
import { Icon } from "@develop/fe-library"

import { accountsDifferenceUserSettingsSelector } from "selectors/userSettingsSelectors"

import { useSubscription, useUrlParams, useUserSettingsCommon } from "hooks"

import l from "utils/intl"

import { ASYNC_STATUSES } from "constants/async"
import { PRODUCT_NAMES } from "constants/product"
import {
  SERVICE_PHONE_NUMBER,
  SERVICE_PHONE_NUMBER_FORMATTED,
} from "constants/subscription"
import { USER_SETTINGS_KEYS } from "constants/user"

import { checkIsAccountsDifference } from "./utils"

import { MESSAGE } from "./constants"

import { AsyncStatus, DashboardFiltersParams } from "types"

export const useAccountDifferenceAlert = () => {
  const {
    isBasSubscriptionActive,
    accountDifference,
    accountDifferenceList,
    accountDifferenceLength,
  } = useSubscription()

  const { getUserSettings, createUserSettings, updateUserSettings } =
    useUserSettingsCommon()

  const accountsDifferenceUserSettings = useSelector(
    accountsDifferenceUserSettingsSelector,
  )

  const [isShown, setIsShown] = useState(false)
  const [loading, setLoading] = useState<AsyncStatus>(ASYNC_STATUSES.IDLE)

  const successCallback = () => setLoading(ASYNC_STATUSES.FULFILLED)

  const failureCallback = () => setLoading(ASYNC_STATUSES.REJECTED)

  useEffect(() => {
    if (accountDifferenceLength > 0) {
      setLoading(ASYNC_STATUSES.PENDING)

      getUserSettings({
        key: USER_SETTINGS_KEYS.accountsDifferenceUserSettings,
        successCallback,
        failureCallback,
      })
    }
  }, [accountDifferenceLength])

  // DESC: update `isShown` state from `accountsDifferenceUserSettings`
  useEffect(() => {
    if (loading !== ASYNC_STATUSES.FULFILLED) {
      return
    }

    const shouldShowAlert: boolean = checkIsAccountsDifference(
      accountDifferenceList,
      accountsDifferenceUserSettings?.value,
    )

    setIsShown(shouldShowAlert)
  }, [loading, accountDifferenceList, accountsDifferenceUserSettings])

  const handleAlertClose = (): void => {
    const accountDifferenceUserSettingsId: number =
      accountsDifferenceUserSettings.id

    setLoading(ASYNC_STATUSES.PENDING)

    if (!accountDifferenceUserSettingsId) {
      createUserSettings({
        key: USER_SETTINGS_KEYS.accountsDifferenceUserSettings,
        value: accountDifferenceList,
        successCallback,
        failureCallback,
      })
    } else {
      updateUserSettings({
        id: accountDifferenceUserSettingsId,
        value: accountDifferenceList,
        successCallback,
        failureCallback,
      })
    }
  }

  const { urlParams } = useUrlParams<DashboardFiltersParams>()

  const { shouldShowAlert, messageText, action } = useMemo(() => {
    const isFromRepricer: boolean = urlParams.isFromRepricer === "1"

    // There are 2 types of alert texts
    // 1. Subscription active with account difference
    // 2. Subscription active with account difference from a repricer link
    const isBasHasAccountsDifference: boolean =
      isBasSubscriptionActive && !!accountDifferenceLength

    let messageTextContent: string = ""
    let messageText: string = ""

    // DESC: Subscription active with accounts difference and maybe from a repricer link
    if (isBasHasAccountsDifference) {
      if (isFromRepricer) {
        messageTextContent = MESSAGE.FROM_REPRICER

        const account = accountDifference.find(
          (account) => account?.sellerId === urlParams.productSeller,
        )

        if (!account) {
          return {
            shouldShowAlert: false,
            messageText: "",
            action: null,
          }
        }

        const accountName: string = account?.customerAccount?.title

        messageText = l(messageTextContent, {
          accountName: <b>{accountName}</b>,
          productName: PRODUCT_NAMES["bas"],
          phone: (
            <a href={`tel:${SERVICE_PHONE_NUMBER_FORMATTED}`}>
              {SERVICE_PHONE_NUMBER}
            </a>
          ),
        })
      } else {
        messageTextContent = MESSAGE.ACCOUNTS_DIFFERENCE

        messageText = l(messageTextContent, {
          accountsList: <b>{accountDifferenceList}</b>,
          accountsCount: accountDifferenceLength,
          productName: PRODUCT_NAMES["bas"],
          phone: (
            <a href={`tel:${SERVICE_PHONE_NUMBER_FORMATTED}`}>
              {SERVICE_PHONE_NUMBER}
            </a>
          ),
        })
      }
    }

    const buttonClose = (
      <Icon name="icnClose" size="--icon-size-4" onClick={handleAlertClose} />
    )

    let action: unknown

    if (!isFromRepricer) {
      action = buttonClose
    }

    const shouldShowAlert: boolean =
      isShown && isBasSubscriptionActive && isBasHasAccountsDifference

    return {
      shouldShowAlert,
      messageText,
      action,
    }
  }, [
    accountDifference,
    accountDifferenceLength,
    accountDifferenceList,
    isBasSubscriptionActive,
    isShown,
    urlParams.isFromRepricer,
    urlParams.productSeller,
  ])

  return {
    shouldShowAlert,
    messageText,
    action,
  }
}
