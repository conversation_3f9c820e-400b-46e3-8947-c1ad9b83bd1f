import React from "react"
import { Box } from "@develop/fe-library"

import FormattedMessage from "components/FormattedMessage"

import { PRODUCT_NAMES } from "constants/product"

import { RestrictedWizardLink } from "./components/RestrictedWizardLink/RestrictedWizardLink"

export const SetupWizardLink = () => {
  return (
    <Box display="inline-flex" columnGap="s">
      <FormattedMessage
        id="<span>Setup {productName}</span> to get full access"
        defaultMessage="<span>Setup {productName}</span> to get full access"
        values={{
          productName: PRODUCT_NAMES.bas,
          span: (chunks: string) => (
            <RestrictedWizardLink>{chunks}</RestrictedWizardLink>
          ),
        }}
      />
    </Box>
  )
}
