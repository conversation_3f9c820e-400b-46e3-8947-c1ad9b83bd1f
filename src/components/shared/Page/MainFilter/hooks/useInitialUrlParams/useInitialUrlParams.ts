import { useCallback } from "react"
import { useSelector } from "react-redux"
import { useLocation } from "react-router-dom"
import { getUrlSearchParams } from "@develop/fe-library/dist/utils"

import { defaultCurrencyByHomeMarketplaceSelector } from "selectors/mainStateSelectors"

import { useDateRange } from "hooks"

import { validateInputMode } from "utils/dateRange"
import { removeNullAndUndefined } from "utils/objectHelpers"

import type { UrlParam, UrlParams } from "types/UrlParams"

export const useInitialUrlParams = <Params extends UrlParams = UrlParams>() => {
  const { search } = useLocation()

  const defaultCurrencyByHomeMarketplace = useSelector(
    defaultCurrencyByHomeMarketplaceSelector,
  )

  const { getInitialDateRange } = useDateRange()

  // Initiate and validate params:
  // - groups/accounts
  // - marketplaces
  // - date range (from, to, inputMode, period)
  // - currency
  const getInitialUrlParams = useCallback(
    (userSettingsParams: Params = {} as Params): Params => {
      const urlParams = getUrlSearchParams<Params>({
        locationSearch: search,
      })

      // HINT: should we add groupId as well?
      const sellerId: UrlParam =
        urlParams.sellerId || userSettingsParams.sellerId

      const marketplaces: UrlParam =
        urlParams.marketplaces || userSettingsParams.marketplaces

      const { from, to } = getInitialDateRange(userSettingsParams)

      const inputMode = validateInputMode(
        urlParams.inputMode || userSettingsParams.inputMode,
      )

      const currency_id: string =
        urlParams.currency_id ||
        userSettingsParams.currency ||
        userSettingsParams.currency_id ||
        defaultCurrencyByHomeMarketplace

      const initialUrlParams = removeNullAndUndefined({
        sellerId,
        marketplaces,
        from,
        to,
        inputMode,
        currency_id,
      }) as unknown as Params

      return initialUrlParams
    },
    [defaultCurrencyByHomeMarketplace, getInitialDateRange, search],
  )

  return {
    getInitialUrlParams,
  }
}
