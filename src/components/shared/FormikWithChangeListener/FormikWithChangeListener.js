import React, { useEffect, useRef, useState } from "react"
import { getObjectKeys } from "@develop/fe-library/dist/utils"
import { Formik } from "formik"
import { isEqual as lodashIsEqual } from "lodash"
import PropTypes from "prop-types"

import usePrevious from "hooks/usePrevious"

const isEqual = (a, b) => {
  if (!!a !== !!b) return false
  if (typeof a === "object" || typeof b === "object") {
    if (Array.isArray(a) || Array.isArray(b)) {
      return lodashIsEqual(a || [], b || [])
    }
    const values = [...new Set([...getObjectKeys(a), ...getObjectKeys(b)])]

    return !values.some((value) => !isEqual(a[value], b[value]))
  }
  // eslint-disable-next-line
  return a == b
}

/**
 *
 * @param {isResetChangesStateByInit} props If `true` resetting all changes, prev values and initial values in the component state.
 * @returns
 */
const FormikWithChangeListener = (props) => {
  const {
    initialValues: propInitialValues,
    onChangeState,
    validate,
    innerRef: propInnerRef,
    isResetChangesStateByInit,
  } = props
  const innerRef = useRef()
  const [values, setValues] = useState()
  const [allChanges, setAllChanges] = useState([])
  const [stateInitialValues, setStateInitialValues] = useState()
  const prev = usePrevious({ values })

  useEffect(() => {
    const ref = innerRef.current
    let initialValues = { ...(stateInitialValues || propInitialValues || {}) }
    let prevValues = prev?.values || initialValues

    if (ref && !ref.dirty) initialValues = { ...ref.values }

    if (isResetChangesStateByInit) {
      setAllChanges([])
      initialValues = propInitialValues || {}
      prevValues = initialValues
    }

    if (!values || !prevValues || lodashIsEqual(values, prevValues)) return

    const lastChanges = []
    const valueProps = [
      ...new Set([...getObjectKeys(values), ...getObjectKeys(prevValues)]),
    ]

    valueProps.forEach((prop) => {
      // eslint-disable-next-line
      if (!isEqual(values[prop], prevValues[prop])) {
        lastChanges.push({
          name: prop,
          value: values[prop],
          prevValue: prevValues[prop],
          initialValue: initialValues[prop],
        })
      }
      // eslint-disable-next-line
      if (!isEqual(values[prop], initialValues[prop])) {
        setAllChanges((state) => [
          ...state,
          {
            name: prop,
            value: values[prop],
            initialValue: initialValues[prop],
          },
        ])
      }
    })
    const isChanged = allChanges.some(
      ({ value, initialValue }) => !isEqual(value, initialValue),
    )

    onChangeState(lastChanges, allChanges, {
      isChanged,
      initialValues: initialValues,
      prevValues: prevValues,
      values: { ...values },
    })
    stateInitialValues !== initialValues && setStateInitialValues(initialValues)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [values])

  return (
    <Formik
      {...props}
      validateOnChange
      innerRef={(ref) => {
        if (!ref) return
        innerRef.current = ref
        if (propInnerRef) {
          typeof propInnerRef === "function"
            ? propInnerRef(ref)
            : (propInnerRef.current = ref)
        }
      }}
      validate={(values) => {
        setValues({ ...values })
        if (validate) return validate(values)
      }}
    />
  )
}

FormikWithChangeListener.propTypes = {
  initialValues: PropTypes.object,
  onChangeState: PropTypes.func.isRequired,
  isResetChangesStateByInit: PropTypes.bool,
}

FormikWithChangeListener.defaultProps = {
  initialValues: {},
  onChangeState: () => {},
  isResetChangesStateByInit: false,
}

export default FormikWithChangeListener
