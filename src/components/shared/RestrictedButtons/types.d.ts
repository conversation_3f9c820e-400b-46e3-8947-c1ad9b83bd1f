import { AbstractTooltipProps } from "antd"
import React from "react"

interface ButtonProps {
  className?: string
  buttonSize?: string
  children?: any
  icon?: any
  onClick?: () => void
  disabled?: boolean
}

export type ButtonPopoverProps = AbstractTooltipProps & {
  content?: string
  disabled?: boolean
  onClick: () => void
  type?: string
}

export interface CompoundedComponent
  extends React.ForwardRefExoticComponent<
    ButtonProps & React.RefAttributes<unknown>
  > {
  __ANT_BUTTON?: true
}
