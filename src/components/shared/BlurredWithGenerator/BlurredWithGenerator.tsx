import React from "react"

import { useGeneratedValue } from "hooks"

import { Blurred } from "../Blurred"

import { BlurredWithGeneratorProps } from "./BlurredWithGeneratorTypes"

export const BlurredWithGenerator = ({
  children,
  magnitude,
  variability,
  toFixed,
}: BlurredWithGeneratorProps) => {
  const generatedValue = useGeneratedValue({
    magnitude,
    variability,
    toFixed,
    shouldGenerate: !children,
  })

  const blurredContent = children ?? generatedValue

  return <Blurred>{blurredContent}</Blurred>
}
