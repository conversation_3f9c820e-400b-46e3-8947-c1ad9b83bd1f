import React, { useRef } from "react"
import cn from "classnames"
import { TimelineTypes, TimelineItemPointProps } from "./types"

import { useResizeObserver } from "hooks/useResizeObserver"

import styles from "./timeline.module.scss"

const Timeline = ({
  items = [],
  renderLeft,
  renderRight,
  renderItemPoint,
  customClassName,
  onClickItemPoint,
  renderLeftKey = "date_start",
  showItemPoint = () => true,
}: TimelineTypes) => {
  const rightColumnRef = useRef<HTMLDivElement | null>(null)

  const [width] = useResizeObserver({
    ref: rightColumnRef,
    callback: () => {},
  })

  const setRef = (index: number) => (index === 0 ? { ref: rightColumnRef } : {})

  const renderTimelineItemPoint = ({ item }: TimelineItemPointProps) => {
    if (!showItemPoint(item)) {
      return null
    }

    return (
      <div className={styles.timelineItemPoint}>
        {renderItemPoint ? (
          renderItemPoint(item)
        ) : (
          <span
            onClick={onClickItemPoint}
            className={styles.timelineItemPointIcon}
          />
        )}
      </div>
    )
  }

  return !items.length ? null : (
    <div className={cn(styles.timeline, customClassName)}>
      <span className={styles.separator} style={{ right: `${width}px` }} />

      {items.map((item, index) => (
        <div className={styles.timelineItem} key={item.id}>
          <div className={styles.timelineItemLeftWrapper}>
            <div
              className={cn(styles.timelineItemLeft, {
                [styles.noItem]: !item[renderLeftKey],
              })}
            >
              {renderLeft(item)}
            </div>
          </div>
          <div className={styles.timelineItemRight} {...setRef(index)}>
            {renderRight(item)}

            {renderTimelineItemPoint({ item })}
          </div>
        </div>
      ))}
    </div>
  )
}

export default Timeline
