@import 'assets/styles/variables';

.marketplaceGroup:not(:last-child) {
  margin-bottom: 20px;
}

.marketplacesContainer {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
  list-style: none;
  margin: 0;
  padding: 0;

  @media screen and (max-width: $sm) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media screen and (max-width: $size320) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.groupHeader {
  margin-bottom: 10px;
}

.title.title {
  color: $text_main;
  font-size: 14px;
  font-weight: 700;
}

.marketplaceItem {
  align-items: center;
  border: 1px solid $border_main;
  border-radius: 2px;
  color: $text_main;
  cursor: pointer;
  display: flex;
  padding: 10px;
  width: 100%;

  @media screen and (max-width: $sm) {
    width: auto;
  }

  &:hover {
    border-color: $border_active;
  }

  &.selected {
    background-color: $int_active_on;
    border-color: $border_active;
    color: $white_text;
  }

  &.disabled {
    background-color: $disable_bg;
  }
}

.flagIcon {
  border: 1px solid $hover_grid;
  border-radius: 34px;
  height: 24px;
  margin-right: 10px;
  width: 24px;
}
