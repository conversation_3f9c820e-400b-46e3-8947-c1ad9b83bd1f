import React, { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import {
  Box,
  Collapse,
  Flag,
  Icon,
  Modal,
  Typography,
} from "@develop/fe-library"

import { ordersActions } from "actions/ordersActions"

import {
  getAmazonAccountBySellerIdSelector,
  getAmazonMarketplaceByIdSelector,
} from "selectors/mainStateSelectors"
import {
  getCurrentOrderSelector,
  getIsModalDataLoadingSelector,
  getIsModalVisibleSelector,
  getOrderDetailsBreakdownSelector,
  getOrderDetailsSelector,
} from "selectors/ordersSelectors"

import { OrderNumberLink } from "components/OrderNumberLink"
import { OrderStatusTag } from "components/OrderStatusTag"
import { ProductInfo } from "components/ProductInfo"
import { List, ListItem } from "components/shared/List"

import { checkIsArray } from "utils/arrayHelpers"
import { countryCode } from "utils/countryCode"
import { convertToLocalDateTime } from "utils/dateConverter"
import l from "utils/intl"
import ln from "utils/localeNumber"
import { checkIsFunction } from "utils/validationHelper"

import GridProduct from "models/GridProduct"

import { EstimatedValuesAlert, OrderBreakdownCollapse } from "../../components"

import type { OrderDetailsModalProps } from "./OrderDetailsModalTypes"

import styles from "../../orders.module.scss"

const { displayModal, getAmazonOrderDetails } = ordersActions

export const OrderDetailsModal = ({ onEdit }: OrderDetailsModalProps) => {
  const dispatch = useDispatch()

  const isModalVisible = useSelector(getIsModalVisibleSelector)
  const isModalDataLoading = useSelector(getIsModalDataLoadingSelector)
  const getOrderDetailsBreakdown = useSelector(getOrderDetailsBreakdownSelector)
  const orderDetails = useSelector(getOrderDetailsSelector)
  const currentOrder = useSelector(getCurrentOrderSelector)

  const {
    updated_at = "",
    summary,
    products,
    currency_id: currencyId = "",
    is_approximate_amounts_calculation: isApproximateAmountsCalculation,
  } = orderDetails ?? {}

  const handleEdit =
    checkIsFunction(onEdit) && !!currentOrder
      ? () => onEdit(currentOrder)
      : null

  const { customerAccount } = useSelector((state) =>
    getAmazonAccountBySellerIdSelector(state, summary?.seller_id),
  )
  const { country, title: marketplaceTitle } = useSelector((state) =>
    getAmazonMarketplaceByIdSelector(state, summary?.marketplace_id),
  )

  const {
    marketplace_id: marketplaceId = "",
    order_id: orderId,
    purchase_date: purchaseDate = "",
    status,
    sales_total: salesTotal,
    offer_type: offerType,
  } = summary ?? {}

  const handleModalClose = () => {
    dispatch(displayModal({ isModalVisible: false }))
  }

  useEffect(() => {
    if (isModalVisible) {
      dispatch(getAmazonOrderDetails({}))
    }
  }, [dispatch, isModalVisible])

  const isSalesTotalOriginal =
    salesTotal?.original?.amount && salesTotal?.original?.currency_id
  const isSalesTotalChosen =
    salesTotal?.chosen?.amount && salesTotal?.chosen?.currency_id

  const salesTotalOriginal = isSalesTotalOriginal
    ? ln(salesTotal.original.amount, 2, {
        currency: salesTotal.original.currency_id,
      })
    : l("N/A")

  const salesTotalChosen = isSalesTotalChosen
    ? ln(salesTotal?.chosen?.amount, 2, {
        currency: salesTotal.chosen.currency_id,
      })
    : l("N/A")

  const summaryItems = [
    {
      key: 1,
      label: l("Amazon account name"),
      value: customerAccount?.title,
    },
    {
      key: 2,
      label: l("Order number"),
      value: (
        <OrderNumberLink marketplaceId={marketplaceId} orderId={orderId} />
      ),
    },
    {
      key: 3,
      label: l("Order date"),
      value: convertToLocalDateTime(purchaseDate),
    },
    {
      key: 4,
      label: l("Order status"),
      value: <OrderStatusTag status={status} />,
    },
    {
      key: 5,
      label: l("Offer type"),
      value: offerType,
    },
    {
      key: 6,
      label: l("Marketplace"),
      value: (
        <Box align="center" gap="m">
          <Flag
            borderRadius="--border-radius-circle"
            className={styles.flag}
            locale={countryCode(country)}
            size={14}
          />
          {marketplaceTitle}
        </Box>
      ),
    },
    {
      key: 7,
      label: l("Sales total (Original currency / Chosen currency)"),
      value: (
        <span>
          <span dir="ltr">{salesTotalOriginal}</span> /{" "}
          <span dir="ltr">{salesTotalChosen}</span>
        </span>
      ),
    },
  ]

  const productItems = [
    {
      key: 1,
      header: ({ isExpanded }: { isExpanded: boolean }) => {
        const chevronIcnName = isExpanded ? "icnChevronUp" : "icnChevronDown"

        return (
          <Box
            gap="m"
            justify="space-between"
            padding="m"
            tb={{
              padding: "m l",
            }}
          >
            <Box gap="m">
              <Icon name={chevronIcnName} size="--icon-size-4" />
              <Typography variant="--font-body-text-3">
                {l("Products")}
              </Typography>
            </Box>
            <Typography variant="--font-body-text-3">
              {products?.length}
            </Typography>
          </Box>
        )
      },
      contents: (
        <Box className={styles.productsWrapper} flexDirection="column">
          {products?.map((product) => {
            // @ts-expect-error
            const gridProduct = new GridProduct({ ...product, ...summary })
            const orderImageUrl = gridProduct.getImageUrl()

            return (
              <ProductInfo
                key={product.asin}
                currencyId={currencyId}
                imageUrl={orderImageUrl}
                marketplaceId={marketplaceId}
                product={product}
                onEdit={handleEdit}
              />
            )
          }) || null}
        </Box>
      ),
    },
  ]

  return (
    <Modal
      visible
      title={l("Order details")}
      width="--modal-size-m"
      onCancel={handleModalClose}
      onClose={handleModalClose}
    >
      {isModalDataLoading ? null : (
        <Box flexDirection="column" gap="m" tb={{ gap: "l" }}>
          {isApproximateAmountsCalculation ? <EstimatedValuesAlert /> : null}

          <Typography
            color="--color-text-second"
            variant="--font-body-text-7"
          >{`${l("Last update")}: ${convertToLocalDateTime(
            updated_at,
          )}`}</Typography>

          <Box flexDirection="column" gap="s">
            <Typography variant="--font-body-text-3">{l("Summary")}</Typography>

            <List isWrappedOnMobile>
              {summaryItems.map(({ key, label, value }) => (
                <ListItem key={key} name={label} value={value} />
              ))}
            </List>
          </Box>

          {checkIsArray(products) ? (
            <Collapse
              hasLeftAndRightBorders
              hasChevron={false}
              headerGap="0"
              headerPadding="0"
              isEventPropagationStopped={false}
              items={productItems}
            />
          ) : null}

          <Box flexDirection="column" gap="s">
            <Typography variant="--font-body-text-3">
              {l("Order breakdown")}
            </Typography>

            <OrderBreakdownCollapse
              collapseData={getOrderDetailsBreakdown}
              currencyId={currencyId}
            />
          </Box>
        </Box>
      )}
    </Modal>
  )
}
