import { useCallback, useEffect } from "react"
import { useFormContext } from "react-hook-form"

import { useCompareForm, useSegments } from "components/SegmentsSidebar/hooks"

import { useUrlParams } from "hooks"

import { useTransformValues } from "../useTransformValues"
import { useWatchFilters } from "../useWatchFilters"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { DashboardFiltersParams } from "types"

import { UseFiltersProps } from "./useFiltersTypes"

export const useFilters = ({
  isDefaultValuesInitiated = false,
  onRefetchFormFilters,
}: UseFiltersProps) => {
  const form = useFormContext<DashboardFiltersFormType>()
  const { reset } = form

  useWatchFilters()

  const { segmentToEdit, clearSegmentToEdit } = useSegments()
  const { urlParams } = useUrlParams<DashboardFiltersParams>()
  const { compareFormValuesWithUrlParams } = useCompareForm()
  const { transformUrlParamsToValues } = useTransformValues()

  // On mount
  const filtersOnMount = useCallback(() => {
    const shouldValidateFormOnMount: boolean =
      !segmentToEdit && isDefaultValuesInitiated

    if (!shouldValidateFormOnMount) {
      return
    }

    const values = form.getValues()

    const compared = compareFormValuesWithUrlParams(values)

    if (!compared.isDirty) {
      return
    }

    const restoredValues = transformUrlParamsToValues(urlParams)

    const onSuccess = (): void => {
      reset(restoredValues)
    }

    onRefetchFormFilters({
      urlParams,
      values: restoredValues,
      onSuccess,
    })
  }, [
    compareFormValuesWithUrlParams,
    form,
    isDefaultValuesInitiated,
    onRefetchFormFilters,
    reset,
    segmentToEdit,
    transformUrlParamsToValues,
    urlParams,
  ])

  useEffect(() => {
    filtersOnMount()

    return () => {
      clearSegmentToEdit()
    }
  }, [])

  return {
    segmentToEdit,
  }
}
