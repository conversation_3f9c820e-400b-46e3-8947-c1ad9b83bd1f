import React from "react"
import { Box, Flag, Typography } from "@develop/fe-library"

import { CURRENCIES_NAMES } from "constants/currencies"

import { CurrencySelectProps } from "./CurrencyOptionTypes"

export const CurrencyOption = ({ option }: CurrencySelectProps) => {
  const locale = CURRENCIES_NAMES[option.value]?.countryCode?.toLowerCase()

  return (
    <Box gap="m" align="center">
      <Flag size={16} locale={locale} borderRadius="--border-radius-circle" />

      <Typography variant="--font-body-text-7" color="--color-text-main">
        {option.label}
      </Typography>
    </Box>
  )
}
