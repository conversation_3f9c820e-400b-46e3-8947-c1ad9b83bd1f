import { BaseSyntheticEvent } from "react"
import { FieldErrors } from "react-hook-form"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"
import { FiltersSaveAsSegmentFormType } from "components/SegmentsSidebar/components/FiltersSaveAsSegmentModal/FiltersSaveAsSegmentModalTypes"

export type HandleSaveAsProps = {
  name: string
  values: DashboardFiltersFormType
  id?: number
  onSuccess?: () => void
  onFailure?: (errors: FieldErrors<FiltersSaveAsSegmentFormType>) => void
}

export type UseFiltersFooterProps = {
  onFormReset: () => void
  onFormSubmit: (event?: BaseSyntheticEvent) => Promise<void>
}
