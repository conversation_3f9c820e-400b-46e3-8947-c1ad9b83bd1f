import React from "react"
import { Collapse } from "@develop/fe-library"

import { ProductFilterByAdultProduct } from "./components"

import { useProductFilters } from "./hooks"

export const ProductFilters = () => {
  const { activeProductFilters, collapsibleItems, handleCollapseChange } =
    useProductFilters()

  return (
    <>
      <Collapse
        activeKeys={activeProductFilters}
        hasChevron={false}
        headerPadding="0"
        items={collapsibleItems}
        onChange={handleCollapseChange}
      />

      <ProductFilterByAdultProduct />
    </>
  )
}
