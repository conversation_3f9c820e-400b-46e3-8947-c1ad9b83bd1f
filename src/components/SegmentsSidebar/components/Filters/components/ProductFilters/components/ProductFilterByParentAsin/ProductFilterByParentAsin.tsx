import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react"
import { useFormContext, useWatch } from "react-hook-form"
import { FormItems } from "@develop/fe-library"
import debounce from "lodash/debounce"

import { useProductFilterDropdownMenu } from "components/SegmentsSidebar/components/Filters/components/ProductFilters/hooks"
import {
  buildProductFilterOptions,
  BuildProductFilterOptionType,
  makeOptionsUnique,
} from "components/SegmentsSidebar/components/Filters/components/ProductFilters/utils"
import {
  useCompareForm,
  useFetchProducts,
  useProductsAndFilters,
} from "components/SegmentsSidebar/hooks"

import l from "utils/intl"

import { ProductFilterSelectOption } from "../ProductFilterSelectOption"

import { DashboardFiltersFormType } from "components/SegmentsSidebar/components/Filters/FiltersTypes"

import { ProductOptionType } from "types/store/salesHistoryReducer"

import { IsTogglePreventedParams } from "@develop/fe-library/dist/lib/components/Select/SelectTypes"

export const ProductFilterByParentAsin = () => {
  const form = useFormContext<DashboardFiltersFormType>()
  const [productId] = useWatch({
    control: form.control,
    name: ["productId"],
  })

  const {
    isLoadingProductsOrFilters,
    products,
    isProductsIdle,
    isProductsLoading,
    isProductsFulfilledOrRejected,
    findProduct,
    hasProducts,
    handleClearProducts,
  } = useProductsAndFilters()
  const { fetchProducts } = useFetchProducts()
  const { renderProductDropdownMenu } = useProductFilterDropdownMenu()
  const { getIsAnyProductFieldInForm } = useCompareForm()

  const [searchValue, setSearchValue] = useState("")

  const options: ProductOptionType[] = useMemo(() => {
    const selectedProduct = findProduct(productId)

    const optionsParams: BuildProductFilterOptionType = {
      valueKey: "parent_asin",
      labelKey: "parent_asin",
    }

    const options = selectedProduct ? [selectedProduct] : products

    return (
      options
        .map(buildProductFilterOptions(optionsParams))
        .filter(Boolean) as ProductOptionType[]
    ).reduce<ProductOptionType[]>(makeOptionsUnique, [])
  }, [findProduct, productId, products])

  const searchProduct = useCallback(
    (value: string): void => {
      const values = form.getValues()

      fetchProducts({
        values,
        searchValue: {
          key: "parent_asin",
          value,
        },
      })
    },
    [fetchProducts, form],
  )

  const searchProductDebounced = useCallback(debounce(searchProduct, 500), [])

  const handleSearchValueChange = (value: string) => {
    setSearchValue(value.trim())
  }

  const handleIsTogglePrevented = useCallback(
    ({ open }: IsTogglePreventedParams): boolean => {
      return open
    },
    [],
  )

  const clearProducts = useCallback(() => {
    const values = form.getValues()

    const isAnyProductFieldInForm = getIsAnyProductFieldInForm(values)

    if (isAnyProductFieldInForm) {
      return
    }

    handleClearProducts()
  }, [form, getIsAnyProductFieldInForm, handleClearProducts])

  const handleBlur = useCallback(() => {
    clearProducts()
  }, [clearProducts])

  useEffect(() => {
    const isAllowedToSearch: boolean = searchValue.trim().length >= 3

    if (isAllowedToSearch) {
      if (hasProducts) {
        return
      }

      searchProductDebounced(searchValue)

      return
    }

    const isAllowedToClear: boolean = searchValue.trim().length === 0

    if (isAllowedToClear) {
      clearProducts()
    }
  }, [searchValue])

  useEffect(() => {
    return () => {
      searchProductDebounced.cancel()
    }
  }, [searchProductDebounced])

  const renderOption = useCallback(
    ({ option }: { option: ProductOptionType }) => {
      return (
        <ProductFilterSelectOption
          isMarketplaceHidden
          customKeyToDisplay="parent_asin"
          customValueToDisplay={"Parent ASIN"}
          isAsin={false}
          option={option}
          searchValue={searchValue}
        />
      )
    },
    [searchValue],
  )

  const isOptionsEmpty: boolean = !options.length
  const isDisabled: boolean = !!productId || isLoadingProductsOrFilters
  const isIdle: boolean = isProductsIdle && (!productId || isOptionsEmpty)

  return (
    <FormItems
      // @ts-expect-error
      form={form}
      items={[
        {
          type: "select",
          name: "parent_asin",
          inputProps: {
            label: l("Search"),
            options,
            hasClearIcon: true,
            hasChevronIcon: false,
            hasSearch: true,
            hasSearchIcon: false,
            optionHeight: 60,
            searchValue,
            isGlobal: true,
            isDisabled,
            isTogglePrevented: handleIsTogglePrevented,
            maxVisibleOptions: 5,
            renderOption,
            renderDropdownMenu: renderProductDropdownMenu({
              title: l("Parent"),
              isIdle,
              isLoading: isProductsLoading,
              isEmpty: isProductsFulfilledOrRejected && isOptionsEmpty,
            }),
            onSearch: handleSearchValueChange,
            onBlur: handleBlur,
          },
          gridItemProps: {
            always: 12,
          },
        },
      ]}
    />
  )
}
