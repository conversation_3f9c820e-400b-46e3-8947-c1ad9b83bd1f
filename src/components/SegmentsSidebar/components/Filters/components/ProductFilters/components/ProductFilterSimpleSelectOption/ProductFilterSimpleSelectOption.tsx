import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react"
import cn from "classnames"
import { Box, Typography } from "@develop/fe-library"
import HighlightWords from "react-highlight-words"
import TextEllipsis from "react-text-ellipsis/src"

import { ProductType } from "types/store/salesHistoryReducer"

import { ProductFilterSimpleSelectOptionProps } from "./ProductFilterSimpleSelectOptionTypes"

import styles from "./productFilterSimpleSelectOption.module.scss"

export const ProductFilterSimpleSelectOption = ({
  option,
  searchValue,
  onSelect,
  customValueToDisplay,
  customKeyToDisplay,
}: ProductFilterSimpleSelectOptionProps) => {
  const handleProductSelect =
    (option: ProductType): MouseEventHandler =>
    (event): void => {
      event.stopPropagation()
      event.preventDefault()

      onSelect?.(option)
    }

  return (
    <Box
      key={option.id}
      padding="m"
      gap="m"
      maxWidth="100%"
      tb={{
        maxWidth: "240px",
      }}
    >
      <Box gap="s" onClick={handleProductSelect(option)}>
        <Typography variant="--font-body-text-9" color="--color-text-second">
          {customValueToDisplay}
        </Typography>
        <Typography variant="--font-body-text-9" color="--color-text-second">
          <TextEllipsis
            ellipsisChars="..."
            lines={1}
            tooltip={option[customKeyToDisplay]}
          >
            <HighlightWords
              className={cn(styles.highlight, styles.productSku)}
              autoEscape
              searchWords={[searchValue]}
              textToHighlight={option[customKeyToDisplay]}
            />
          </TextEllipsis>
        </Typography>
      </Box>
    </Box>
  )
}
