import React, { useCallback } from "react"
import { Box, Spinner, Typography } from "@develop/fe-library"

import l from "utils/intl"

export const useProductFilterDropdownMenu = () => {
  const renderProductDropdownMenu = useCallback(
    ({ isIdle, isLoading, isEmpty, title }) =>
      ({ menu }): JSX.Element => {
        if (isIdle) {
          return (
            <Box minWidth="100%" padding="m" align="center" justify="center">
              <Typography variant="--font-body-text-7">
                {l("Search for {title}", { title })}
              </Typography>
            </Box>
          )
        }

        if (isLoading) {
          return (
            <Box minWidth="100%" padding="m" align="center" justify="center">
              <Spinner size="sm" />
            </Box>
          )
        }

        if (isEmpty) {
          return (
            <Box minWidth="100%" padding="m" align="center" justify="center">
              <Typography variant="--font-body-text-7">
                {l("No products found")}
              </Typography>
            </Box>
          )
        }

        return (
          <Box minWidth="100%" display="block">
            {menu}
          </Box>
        )
      },
    [],
  )

  return {
    renderProductDropdownMenu,
  }
}
