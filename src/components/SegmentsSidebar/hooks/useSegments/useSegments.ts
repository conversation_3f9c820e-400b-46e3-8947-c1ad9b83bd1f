import { useCallback, useMemo } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useHistory } from "react-router-dom"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import segmentsActions from "actions/segmentsActions"

import {
  segmentsSelector,
  segmentsStatusSelector,
  segmentToEditSelector,
} from "selectors/segmentsSelectors"

import { useTransformValues } from "components/SegmentsSidebar/components/Filters/hooks"

import { useUrlParams } from "hooks"

import { removeNullAndUndefined } from "utils/objectHelpers"

import {
  AsyncStatus,
  DashboardFiltersParams,
  SegmentRequestBodyType,
} from "types"
import { SegmentType } from "types/store/segments"

const {
  selectSegmentToEdit: selectSegmentToEditAction,
  clearSegmentToEdit: clearSegmentToEditAction,
  getSegments: getSegmentsAction,
  createSegment: createSegmentAction,
  updateSegment: updateSegmentAction,
  deleteSegment: deleteSegmentAction,
} = segmentsActions

export const useSegments = () => {
  const dispatch = useDispatch()
  const history = useHistory()

  const segments = useSelector(segmentsSelector) as SegmentType[]
  const segmentsStatus = useSelector(segmentsStatusSelector) as AsyncStatus
  const segmentToEdit = useSelector(segmentToEditSelector) as SegmentType

  const { urlParams } = useUrlParams<DashboardFiltersParams>()
  const { transformSegmentToUrlParams } = useTransformValues()

  const getSegments = useCallback(
    (onSuccess = () => {}, onFailure = () => {}): void => {
      dispatch(getSegmentsAction(onSuccess, onFailure))
    },
    [dispatch],
  )

  const createSegment = useCallback(
    (
      segment: SegmentRequestBodyType,
      onSuccess = () => {},
      onFailure = () => {},
    ): void => {
      dispatch(createSegmentAction(segment, onSuccess, onFailure))
    },
    [dispatch],
  )

  const updateSegment = useCallback(
    (
      segmentId: number,
      segment: SegmentRequestBodyType,
      onSuccess = () => {},
      onFailure = () => {},
    ): void => {
      dispatch(updateSegmentAction(segmentId, segment, onSuccess, onFailure))
    },
    [dispatch],
  )

  const deleteSegment = useCallback(
    (segmentId: number, onSuccess = () => {}, onFailure = () => {}): void => {
      dispatch(deleteSegmentAction(segmentId, onSuccess, onFailure))
    },
    [dispatch],
  )

  const findSegment = useCallback(
    (id: number | undefined): SegmentType | undefined => {
      if (!id) {
        return undefined
      }

      return segments.find((segment) => segment.id === id)
    },
    [segments],
  )

  const selectSegmentToEdit = useCallback(
    (segment: SegmentType): void => {
      dispatch(selectSegmentToEditAction(segment))
    },
    [dispatch],
  )

  const clearSegmentToEdit = useCallback((): void => {
    dispatch(clearSegmentToEditAction())
  }, [dispatch])

  const applySegment = useCallback(
    (segment: SegmentType): void => {
      if (!segment) {
        return
      }

      const segmentUrlParams = transformSegmentToUrlParams(segment.filters)

      history.push({
        ...history.location,
        search: getUrlSearchParamsString({
          params: removeNullAndUndefined({
            ...urlParams,
            ...segmentUrlParams,
            segmentId: segment.id,
          }),
        }),
      })
    },
    [history, transformSegmentToUrlParams, urlParams],
  )

  const clearAppliedSegment = useCallback((): void => {
    history.push({
      ...history.location,
      search: getUrlSearchParamsString({
        params: removeNullAndUndefined({
          ...urlParams,
          segmentId: undefined,
        }),
      }),
    })
  }, [history, urlParams])

  const getAppliedSegment = useCallback((): SegmentType | undefined => {
    if (!urlParams.segmentId) {
      return
    }

    const segmentId: number = parseInt(urlParams.segmentId, 10)

    return findSegment(segmentId)
  }, [urlParams, findSegment])

  const segmentId: number | undefined = useMemo(() => {
    return urlParams.segmentId ? parseInt(urlParams.segmentId, 10) : undefined
  }, [urlParams])

  return {
    segments,
    segmentsStatus,

    getSegments,
    createSegment,
    updateSegment,
    deleteSegment,

    findSegment,

    selectSegmentToEdit,
    clearSegmentToEdit,
    segmentToEdit,

    segmentId,
    applySegment,
    clearAppliedSegment,
    getAppliedSegment,
  }
}
