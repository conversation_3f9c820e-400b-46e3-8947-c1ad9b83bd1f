import React, { useEffect, useState, memo, useCallback } from "react"
import { useSelector } from "react-redux"
import PropTypes from "prop-types"
import { Empty, Spin } from "antd"
import { useHistory } from "react-router"
import { Typography, Icon } from "@develop/fe-library"
import { getUrlSearchParams } from "@develop/fe-library/dist/utils"

import { getInitialPageSelector } from "selectors/marketplaceSelectors"

import { GroupsAccordion, GroupFilters } from "./components"

import l from "utils/intl"

import styles from "./editGroupsPage.module.scss"

const EditGroupsPageView = ({
  getMarketplaceGroups,
  marketplaces,
  groups,
  searchOptions,
  isLoading,
}) => {
  const history = useHistory()
  const [expandedGroups, setExpandedGroups] = useState([])
  const urlSearchParams = getUrlSearchParams({
    locationSearch: document.location.search,
  })
  const initialPage = useSelector(getInitialPageSelector)

  useEffect(() => {
    const { customerID } = urlSearchParams

    if (customerID) {
      getMarketplaceGroups({})
    } else {
      getMarketplaceGroups({ ...searchOptions, ...urlSearchParams })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleGoToBackPage = useCallback(() => {
    history.push(initialPage)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialPage])

  return (
    <div className={styles.container}>
      <header className={styles.pageHeader}>
        <div className={styles.buttonsContainer}>
          <Icon
            onClick={handleGoToBackPage}
            name="icnChevronLeft"
            color="--color-icon-active"
          />
          <Typography variant="--font-headline-1" className={styles.pageTitle}>
            {l("Edit groups")}
          </Typography>
        </div>
      </header>
      <GroupFilters expandAccordion={setExpandedGroups} groups={groups} />
      {groups.length ? (
        <GroupsAccordion
          marketplaces={marketplaces}
          groups={groups}
          expandedGroups={expandedGroups}
        />
      ) : isLoading ? (
        <Spin />
      ) : (
        <Empty description={l("No data")} className={styles.empty} />
      )}
    </div>
  )
}

EditGroupsPageView.propTypes = {
  getMarketplaceGroups: PropTypes.func.isRequired,
  marketplaces: PropTypes.array,
  groups: PropTypes.array.isRequired,
  searchOptions: PropTypes.object,
  changeSearchOptions: PropTypes.func.isRequired,
  isLoading: PropTypes.bool,
  urlParams: PropTypes.object.isRequired,
}

export default memo(EditGroupsPageView)
