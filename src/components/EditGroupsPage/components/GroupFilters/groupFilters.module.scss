@import "assets/styles/variables";

.container {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
  padding: 0 20px;
  width: 100%;

  @media screen and (max-width: $xs) {
    padding: 0 10px;
    flex-direction: column;
  }
}

.filters {
  flex: 1;

  @media screen and (max-width: $xs) {
    margin-bottom: 10px;
    width: 100%;
  }
}

.filtersForm {
  display: flex;
}

.sortContainer {
  margin-right: 10px;
}

.sortField {
  min-width: 200px;
  margin-bottom: 3px;

  @media screen and (max-width: $lg) {
    min-width: 113px;
  }
}

.searchInputContainer {
  @media screen and (max-width: $md) {
    flex: 1;
  }
}

.searchInput.searchInput.searchInput.searchInput {
  width: 100%;
  padding: 0 10px;
  border-color: $border_main;

  input {
    padding: 0 !important;
  }

  &::placeholder {
    color: $placeholder;
  }

  @media screen and (max-width: $xs) {
    flex: 1;
  }
}

.searchIcon {
  color: $import-icon-color;
  font-size: 14px;
  margin-right: 5px;
}

.controls {
  @media screen and (max-width: $xs) {
    width: 100%;

    .addGroupButton.addGroupButton {
      width: 50%;
    }
  }
}

.desktopButtonsContainer,
.mobileButtonsContainer {
  margin-left: 10px;

  @media screen and (max-width: $xs) {
    margin-left: 0;
    width: 100%;

    & > button {
      flex: 1;
    }
  }
}

.icon {
  svg {
    width: var(--icon-size-2);
    height: var(--icon-size-2);
    fill: var(--color-icon-static);
  }

  &:hover {
    svg {
      fill: var(--color-icon-active);
    }
  }
}

.desktopButtonsContainer {
  display: flex;

  @media screen and (max-width: $md - 1) {
    display: none;
  }
}

.mobileButtonsContainer {
  display: none;

  @media screen and (max-width: $md - 1) {
    display: flex;

    .icon {
      svg {
        width: var(--icon-size-3);
        height: var(--icon-size-3);
        fill: var(--color-icon-clickable);
      }

      &:hover {
        svg {
          fill: var(--color-icon-active);
        }
      }
    }
  }
}
