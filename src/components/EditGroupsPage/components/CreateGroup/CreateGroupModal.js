import React, { useCallback, useEffect, useState } from "react"
import { Modal, TextInput, Typography } from "@develop/fe-library"
import {
  getObjectEntries,
  getObjectValues,
} from "@develop/fe-library/dist/utils"
import cn from "classnames"
import { flatten } from "lodash"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"
import MarketplaceSelector from "components/shared/MarketplaceSelector"

import l from "utils/intl"

import SelectionState from "./components/SelectionState"

import styles from "./createGroup.module.scss"

const CreateGroupModal = ({
  addMarketplaceGroup,
  updateMarketplaceGroup,
  groupedMarketplaces,
  amazonCustomerAccounts,
  isEditMode = false,
  currentGroup,
  onCancel,
  onSuccess,
  isVisible,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [selectedMarketplacesState, setSelectedMarketplaces] = useState([])
  const [selectAll, setSelectAll] = useState(null)
  const [groupName, setGroupName] = useState("")
  const [nameError, setNameError] = useState(null)

  useEffect(() => {
    setIsModalVisible(isVisible)
  }, [isVisible])

  useEffect(() => {
    if (!currentGroup) return
    const items = {}

    currentGroup.amazonCustomerAccount.forEach((account) => {
      const customer = amazonCustomerAccounts.find(
        ({ id }) => account.amazonCustomerAccountId === id,
      )

      if (!customer) return

      const arr = flatten(getObjectValues(groupedMarketplaces)).filter(
        (marketplace) =>
          marketplace.amazon_customer_account_id ===
          account.amazonCustomerAccountId,
      )

      items[customer.customerAccount.title] = arr.filter(({ id }) => {
        return account.amazonMarketplaceIds.includes(id)
      })
    })

    setSelectedMarketplaces(items)
    setGroupName(currentGroup.name)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentGroup, isEditMode])

  const selectAccount = useCallback((name, marketplaces) => {
    setSelectedMarketplaces((prev) => {
      prev[name] = marketplaces

      if (!marketplaces.length) {
        delete prev[name]
      } else {
        prev[name] = marketplaces
      }

      return { ...prev }
    })

    setSelectAll(null)
  }, [])

  const updateCreateCallback = (response) => {
    if (response.name && Array.isArray(response.name)) {
      setNameError(response.name[0])
    } else {
      setGroupName("")
      setNameError(null)
      onCancel()
      onSuccess()
    }
  }

  const createGroup = () => {
    const marketplaces = flatten(getObjectValues(selectedMarketplacesState))
    const payload = {
      name: groupName.trim(),
      accountMarketplaces: marketplaces.map((item) => {
        return {
          amazonMarketplaceId: item.id,
          amazonCustomerAccountId: item.amazon_customer_account_id,
        }
      }),
    }

    isEditMode
      ? updateMarketplaceGroup(
          { ...payload, id: currentGroup.id },
          updateCreateCallback,
        )
      : addMarketplaceGroup(payload, updateCreateCallback)
  }

  const onChangeGroupName = (value) => {
    setGroupName(value)
    setNameError(null)
  }

  const selectedMarketplaces = flatten(
    getObjectValues(selectedMarketplacesState).map((item) => item),
  )
  const canProceed =
    selectedMarketplaces.length >= 2 &&
    groupName.trim().length >= 1 &&
    !nameError

  const modalOkButtonText = l(isEditMode ? "Update" : "Create")

  return (
    <Modal
      bodyClassName={cn(styles.container, "marketplaceGroupModal")}
      cancelButtonText={l("Cancel")}
      okButtonText={modalOkButtonText}
      visible={isModalVisible}
      okButtonProps={{
        disabled: !canProceed,
      }}
      title={
        isEditMode ? (
          <FormattedMessage id="Edit group" />
        ) : (
          <FormattedMessage id="Add new group" />
        )
      }
      onCancel={onCancel}
      onOk={createGroup}
    >
      <form>
        <header className={styles.header}>
          <Typography color="--color-text-main" variant="--font-body-text-7">
            <FormattedMessage id="Choose marketplaces (at least 2 marketplaces are required)*" />
          </Typography>
          <div className={styles.inputGroup}>
            <div className={styles.inputContainer}>
              <TextInput
                isFullWidth
                errorDisplayType={"text"}
                placeholder={l("Group name")}
                value={groupName}
                errorMessage={
                  !!nameError
                    ? l("A group with that name already exists")
                    : null
                }
                onChange={onChangeGroupName}
              />
            </div>
          </div>
        </header>

        {!getObjectValues(selectedMarketplacesState).length ? null : (
          <SelectionState
            selectedMarketplaces={selectedMarketplacesState}
            onSelectAll={setSelectAll}
          />
        )}

        <div className={styles.body}>
          {getObjectEntries(groupedMarketplaces).map(([title, list]) => {
            return (
              <MarketplaceSelector
                key={title}
                groupKey={title}
                marketplaces={list}
                selectAll={selectAll}
                title={title}
                selectedMarketplaces={
                  isEditMode ? selectedMarketplacesState[title] : null
                }
                onChange={selectAccount}
              />
            )
          })}
        </div>
      </form>
    </Modal>
  )
}

CreateGroupModal.propTypes = {
  addMarketplaceGroup: PropTypes.func.isRequired,
  updateMarketplaceGroup: PropTypes.func.isRequired,
  groupedMarketplaces: PropTypes.object.isRequired,
  amazonCustomerAccounts: PropTypes.array,
  isEditMode: PropTypes.bool,
  currentGroup: PropTypes.object,
  onCancel: PropTypes.func.isRequired,
  isVisible: PropTypes.bool.isRequired,
}

export default CreateGroupModal
