import React from "react"
import { FormItems } from "@develop/fe-library"

import { useEditVatForm } from "./hooks"

import type { EditVatFormProps } from "./EditVatFormTypes"

export const EditVatForm = ({ period }: EditVatFormProps) => {
  const { form, items } = useEditVatForm({ period })

  return (
    <FormItems
      boxContainerProps={{ width: "100%" }}
      gridContainerProps={{ gap: "m" }}
      // @ts-expect-error
      form={form}
      items={items}
    />
  )
}
