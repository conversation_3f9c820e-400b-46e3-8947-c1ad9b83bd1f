import React, { useCallback, useMemo } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useForm } from "react-hook-form"
import { Box, Checkbox, FlagSwitchButton } from "@develop/fe-library"

import { transferCostsModalStatesSelector } from "selectors/productCostSelectors"

import productCostActions from "actions/productCostActions"

import { countryCode } from "utils/countryCode"
import l from "utils/intl"

import { FORM_TYPES_TITLES } from "constants/productCost"

import type { TransferCostsModalFormValues } from "components/ProductCostDrawer/components/TransferCostsModal/TransferCostsModalTypes"

const { closeTransferCostsModal, transferCostsForMarketplaces } =
  productCostActions

export const useTransferCostsModal = () => {
  const dispatch = useDispatch()

  const { formType, marketplaces } = useSelector(
    transferCostsModalStatesSelector,
  )

  const form = useForm<TransferCostsModalFormValues>({
    defaultValues: {
      selectedMarketplaces: new Set(),
    },
  })

  const { watch, handleSubmit: formHandleSubmit, setValue, formState } = form

  const selectedMarketplaces = watch("selectedMarketplaces")

  const costTypeTitle: string = FORM_TYPES_TITLES[formType]?.toLowerCase()
  const transferMessage = `Select marketplaces you want to transfer current ${costTypeTitle} to`

  const { isSubmitting } = formState

  const isSubmitButtonDisabled: boolean =
    isSubmitting || selectedMarketplaces.size === 0

  const handleCancel = useCallback((): void => {
    if (isSubmitting) {
      return
    }

    dispatch(closeTransferCostsModal())
  }, [isSubmitting])

  const handleSubmit = useCallback(
    formHandleSubmit(async ({ selectedMarketplaces }) => {
      const target_marketplace_ids = Array.from(selectedMarketplaces).join(",")

      const payload = {
        target_marketplace_ids,
      }

      await dispatch(transferCostsForMarketplaces(payload))
    }),
    [],
  )

  const items = useMemo(() => {
    const isAllSelected: boolean =
      selectedMarketplaces.size === marketplaces.length
    const isSomeSelected: boolean = selectedMarketplaces.size > 0
    const checkboxType =
      !isAllSelected && isSomeSelected ? "selectAll" : "default"
    const isSelectAllCheckboxChecked: boolean = isAllSelected || isSomeSelected

    const handleChangeSelectAll = (isChecked: boolean) => {
      if (!isChecked) {
        setValue("selectedMarketplaces", new Set())
        return
      }

      const allMarketplacesIds = marketplaces.map(({ id }) => id)

      const allMarketplaces = new Set(allMarketplacesIds)

      setValue("selectedMarketplaces", allMarketplaces)
    }

    return [
      {
        type: "component",
        component: (
          <Checkbox
            label={l("Select all")}
            type={checkboxType}
            checked={isSelectAllCheckboxChecked}
            onChange={handleChangeSelectAll}
          />
        ),
        gridItemProps: {
          mSM: 12,
        },
      },
      {
        type: "component",
        component: (
          <Box
            gap="m"
            display="grid"
            mSM={{
              gridTemplateColumns: "repeat(2, 1fr)",
            }}
            mXL={{
              gridTemplateColumns: "repeat(4, 1fr)",
            }}
          >
            {marketplaces.map(({ id, country }) => {
              const flagLocale = countryCode(country)

              const isChecked = selectedMarketplaces.has(id)

              const handleChange = (isCheckedNew: boolean): void => {
                const selectedMarketplacesNew = new Set(selectedMarketplaces)

                if (isCheckedNew) {
                  selectedMarketplacesNew.add(id)
                } else {
                  selectedMarketplacesNew.delete(id)
                }

                setValue("selectedMarketplaces", selectedMarketplacesNew)
              }

              return (
                <FlagSwitchButton
                  locale={flagLocale}
                  isChecked={isChecked}
                  onChange={handleChange}
                />
              )
            })}
          </Box>
        ),
        gridItemProps: {
          mSM: 12,
        },
      },
    ]
  }, [selectedMarketplaces.size, marketplaces])

  return {
    form,
    items,
    marketplaces,
    transferMessage,
    onCancel: handleCancel,
    onSubmit: handleSubmit,
    isSubmitting,
    isSubmitButtonDisabled,
  }
}
