import React from "react"
import { Box, Icon } from "@develop/fe-library"

import { useExpandableMenuWrapper } from "./hooks"

export const ExpandableMenuWrapper = ({ selectedItemIndex, children }) => {
  const { ref, canBeMinified, isExpanded, handleExpand, handleCollapse } =
    useExpandableMenuWrapper({ selectedItemIndex })

  if (!canBeMinified) {
    return (
      <Box
        minWidth="max-content"
        display="block"
        backgroundColor="--color-main-background"
        hasBorder={{ right: true }}
      >
        {children}
      </Box>
    )
  }

  return (
    <Box
      overflow="visible"
      minWidth={60}
      maxWidth={60}
      width={60}
      hasBorder={{ right: true }}
    >
      {!isExpanded ? (
        <Box padding="l m" width="100%" justify="center">
          <Icon name="icnMenu" onClick={handleExpand} />
        </Box>
      ) : (
        <Box
          ref={ref}
          display="block"
          minWidth="max-content"
          zIndex={3}
          hasBorder={{ right: true }}
          backgroundColor="--color-main-background"
          boxShadow="--box-shadow"
        >
          <Box align="center" justify="end" height={42} padding="m l">
            <Icon
              name="icnClose"
              size="--icon-size-2"
              color="--color-icon-clickable"
              onClick={handleCollapse}
            />
          </Box>
          {children}
        </Box>
      )}
    </Box>
  )
}
