import React from "react"

import { S3UploadModal } from "components/Modals"
import { TableWrapper } from "components/shared/TableWrapper"

import { permissionKeys, restrictPopoverMessages } from "constants/permissions"
import { TABLE_SETTINGS_KEY } from "constants/productImport"

import { DownloadTemplateModal, ProductImportErrorsModal } from "./components"

import { useProductImport, useProductImportColumns } from "./hooks"

import { ProductImportProps } from "./ProductImportTypes"

export const ProductImport = ({ pageName = "" }: ProductImportProps) => {
  const {
    dataSource,
    selectFiltersOptions,
    searchOptions,
    totalCount,
    getDataHandler,
    tableGridIcons,
    tableGridButtons,
    isErrorsModalVisible,
    productImportErrors,
    handleErrorModalClose,
    uploadModalVisible,
    uploadError,
    handleUploadModalClose,
    handleUploadFile,
    downloadTemplateError,
    isDownloadTemplateModalVisible,
    closeDownloadTemplateModalHandler,
    downloadTemplateFileHandler,
    isProductImportDataLoading,
  } = useProductImport()

  const { tableColumns } = useProductImportColumns()

  return (
    <>
      <TableWrapper
        // @ts-ignore
        isNeedSort
        actionsColumnWidth={110}
        componentTableSettings={TABLE_SETTINGS_KEY}
        dataSource={dataSource}
        getData={getDataHandler}
        pageTableSettings={TABLE_SETTINGS_KEY}
        searchOptions={searchOptions}
        selectFiltersOptions={selectFiltersOptions}
        tableButtons={tableGridButtons}
        tableColumns={tableColumns}
        tableGridTitle={pageName}
        tableIcons={tableGridIcons}
        tableLoading={isProductImportDataLoading}
        totalCount={totalCount}
      />

      {!isErrorsModalVisible ? null : (
        <ProductImportErrorsModal
          errorsList={productImportErrors}
          title="Product import errors"
          onClose={handleErrorModalClose}
        />
      )}

      {uploadModalVisible ? (
        <S3UploadModal
          accept=".txt,.csv"
          error={uploadError}
          managePermission={permissionKeys.basProductCostImportCreate}
          popoverMessage={restrictPopoverMessages.import}
          title="Upload file"
          onClose={handleUploadModalClose}
          onUpload={handleUploadFile}
        />
      ) : null}

      {isDownloadTemplateModalVisible ? (
        <DownloadTemplateModal
          error={downloadTemplateError}
          managePermission={permissionKeys.basProductCostImportCreate}
          popoverMessage={restrictPopoverMessages.import}
          title="Download template"
          onClose={closeDownloadTemplateModalHandler}
          onDownloadTemplate={downloadTemplateFileHandler}
        />
      ) : null}
    </>
  )
}
