import React from "react"
import PropTypes from "prop-types"
import cn from "classnames"

import Typography from "components/Typography"

import styles from "./fieldErrorItem.module.scss"

export const FieldErrorItem = ({ controls, className }) => {
  if (!controls?.length) {
    return null
  }

  return (
    <div className={cn(styles.item, className)}>
      {controls.map(({ id, title, value, className }) => (
        <div key={id} className={cn(styles.field, className)}>
          <Typography
            className={cn(styles.fieldTitle, "error-field-title")}
            type="div"
            variant="text"
          >
            {title}
          </Typography>
          <Typography
            className={cn(styles.fieldValue, "error-field-value")}
            type="div"
            variant="text"
          >
            {value}
          </Typography>
        </div>
      ))}
    </div>
  )
}

FieldErrorItem.propTypes = {
  controls: PropTypes.array,
  className: PropTypes.string,
}
