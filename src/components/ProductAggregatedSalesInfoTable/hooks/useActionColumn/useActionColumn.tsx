import React, { CSSProperties, useMemo } from "react"
import { useDispatch } from "react-redux"
import type {
  TableRowInteractiveParams,
  TableRowParams,
} from "@develop/fe-library"
import { Box, Icon, Spinner } from "@develop/fe-library"

import { productAggregatedSalesInfoActions } from "actions/productAggregatedSalesInfoActions"
import { GetProductAggregatedSalesInfoTableDataParams } from "actions/productAggregatedSalesInfoActions/ProductAggregatedSalesInfoActionsTypes"

import type {
  ProductAggregatedSalesInfoTableData,
  ProductAggregatedSalesInfoTableFilterValues,
} from "components/ProductAggregatedSalesInfoTable/ProductAggregatedSalesInfoTableTypes"
import {
  actionColumnColSpan,
  actionColumnRowSpan,
} from "components/ProductAggregatedSalesInfoTable/utils"

import { buildProductAggregatedSalesInfoTableSpecialParams } from "utils/buildProductAggregatedSalesInfoTableSpecialParams"

import { ASYNC_STATUSES } from "constants/async"

import type { UseActionColumn } from "./ActionColumnTypes"

export const useActionColumn: UseActionColumn = ({
  buildRequestParams,
  onExpand,
  onCollapse,
  expandedRowStatuses,
}) => {
  const dispatch = useDispatch()

  return useMemo(() => {
    const renderRowActions = ({
      index,
      item,
      isExpanded,
      onToggleExpand,
      getFilterValues,
    }: TableRowInteractiveParams<
      ProductAggregatedSalesInfoTableData,
      ProductAggregatedSalesInfoTableFilterValues
    >) => {
      if (!item.group_value || item.tableRowMetaData?.isExpandedRowChild) {
        return null
      }

      if (expandedRowStatuses[item.id] === ASYNC_STATUSES.PENDING) {
        return (
          <Box align="center" width={30}>
            <Spinner type="circle" />
          </Box>
        )
      }

      const icon = isExpanded ? "icnChevronUp" : "icnChevronDown"

      const handleToggleExpand = () => {
        const filterValues: Partial<ProductAggregatedSalesInfoTableFilterValues> =
          getFilterValues?.() || {}

        if (!filterValues.group_by || !item.group_value) {
          return
        }

        onToggleExpand?.()

        if (isExpanded) {
          // Call onCollapse to clear the stored expanded row items count for this row
          onCollapse(item.id)
          dispatch(productAggregatedSalesInfoActions.collapseRow(item.id))

          return
        }

        // Call onExpand to store the expanded row items count for this row
        onExpand(item.id)

        const requestParams = buildRequestParams(filterValues)

        const requestParamsProcessed: GetProductAggregatedSalesInfoTableDataParams["requestParams"] =
          {
            ...requestParams.requestParams,
            ...buildProductAggregatedSalesInfoTableSpecialParams({
              groupBy: filterValues.group_by || "id",
              groupValue: item.group_value,
              requestParams: requestParams.requestParams,
            }),
          }

        dispatch(
          productAggregatedSalesInfoActions.expandRow({
            params: {
              id: item.id,
              page: 1,
              requestParams: requestParamsProcessed,
            },
          }),
        )
      }

      return (
        <Box align="center" width={30}>
          <Icon name={icon} size="--icon-size-3" onClick={handleToggleExpand} />
        </Box>
      )
    }

    return {
      colSpan: actionColumnColSpan,
      rowSpan: actionColumnRowSpan,
      renderRowActions,
      cellClassName: "HELLO",
      cellStyle: ({
        item,
      }: TableRowParams<ProductAggregatedSalesInfoTableData>): CSSProperties => {
        if (item.tableRowMetaData?.isExpandedRowChild) {
          return {
            background: "white",
            pointerEvents: "none",
          } as CSSProperties
        }

        return {} as CSSProperties
      },
    }
  }, [buildRequestParams, onCollapse, onExpand, expandedRowStatuses])
}
