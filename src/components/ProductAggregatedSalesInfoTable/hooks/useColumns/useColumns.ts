import { useMemo } from "react"
import { useSelector } from "react-redux"
import type { Column } from "@develop/fe-library"

import {
  amazonCustomerAccountsSelector,
  amazonMarketplacesSelector,
} from "selectors/mainStateSelectors"

import { FILTER_BASE_INPUT_PROPS } from "components/ProductAggregatedSalesInfoTable/constants"
import type {
  ProductAggregatedSalesInfoTableData,
  ProductAggregatedSalesInfoTableFilterValues,
} from "components/ProductAggregatedSalesInfoTable/ProductAggregatedSalesInfoTableTypes"

import {
  useGroupAccountOptions,
  useMarketplaceOptions,
  useMinStatsDate,
  useSubscription,
  useUrlParams,
} from "hooks"

import { buildOptionsFromObject } from "utils/buildOptionsFromObject"
import l from "utils/intl"
import ln from "utils/localeNumber"

import { GROUP_ACCOUNT_TYPE } from "constants/groupAccount"
import { PRODUCT_AGGREGATED_SALES_INFO_TABLE_GROUP_BY_LABELS } from "constants/productAggregatedSalesInfoTableGroupBy"

import {
  buildGetOptionLabel,
  getCellValue,
  getPercentValue,
  groupByColumnCellSpan,
} from "../../utils"

import {
  buildRenderAsinCell,
  buildRenderBlurredCell,
  buildRenderEstimatedProfitAmountCell,
  buildRenderOrdersLinkCells,
  buildRenderSellerIdCell,
  buildRenderTransactionsLinkCells,
  renderImageCell,
  renderSellerSkuCell,
  renderTitleCell,
} from "../../renders"

import type { DashboardFiltersParams } from "types"

import type { UseColumns } from "./UseColumnsTypes"

export const useColumns: UseColumns = () => {
  const { amazonMarketplaces } = useSelector(amazonMarketplacesSelector)
  const { amazonCustomerAccounts } = useSelector(amazonCustomerAccountsSelector)

  const { urlParams } = useUrlParams<DashboardFiltersParams>()
  const { minStatsDate, today } = useMinStatsDate()
  const { getMarketplaceOptions } = useMarketplaceOptions()
  const { basAccountsOptions } = useGroupAccountOptions()
  const { hasFreemiumRestrictions } = useSubscription()

  const {
    marketplace_id,
    seller_id = GROUP_ACCOUNT_TYPE.GLOBAL,
    view,
    from,
    to,
    currency_code,
  } = urlParams

  return useMemo((): Array<
    Column<
      ProductAggregatedSalesInfoTableData,
      ProductAggregatedSalesInfoTableFilterValues
    >
  > => {
    const shouldBlur: boolean = hasFreemiumRestrictions

    const marketplaceOptions = getMarketplaceOptions(seller_id).filter(
      (option) => {
        if (!marketplace_id) {
          return option
        }

        return marketplace_id.includes(option.value)
      },
    )

    const { renderUnitsCell, renderOrdersCell, renderRefundsCell } =
      buildRenderOrdersLinkCells({
        dateRangeUrlParams: {
          from,
          to,
        },
      })

    const {
      renderRevenueAmountCell,
      renderTotalIncomeCell,
      renderExpensesAmountCell,
      renderAmazonFeesCell,
    } = buildRenderTransactionsLinkCells({
      view,
      minStatsDate,
      today,
      postedDateRangeParams: {
        from,
        to,
      },
      currencyCode: currency_code,
    })

    return [
      {
        key: "group_by",
        title: "Group by",
        filterKey: "group_by",
        type: "select",
        width: 100,
        inputProps: {
          options: buildOptionsFromObject({
            object: PRODUCT_AGGREGATED_SALES_INFO_TABLE_GROUP_BY_LABELS,
            shouldTranslateLabel: true,
          }),
        },
        dataIndex: "group_value",
        rowSpan: groupByColumnCellSpan,
        colSpan: groupByColumnCellSpan,
        renderCell: (tableEntryParams) => {
          if (tableEntryParams.item.group_by === "marketplace") {
            return buildGetOptionLabel(marketplaceOptions)(tableEntryParams)
          }

          return tableEntryParams.value
        },
      },
      {
        key: "image",
        title: l("Image"),
        sorter: false,
        type: "default",
        minWidth: 80,
        width: 80,
        dataIndex: "image",
        hasEllipsis: false,
        renderCell: renderImageCell,
      },
      {
        key: "product_title",
        title: l("Title"),
        filterKey: "product_title",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 80,
        width: 300,
        dataIndex: "product_title",
        renderCell: renderTitleCell,
      },
      {
        key: "product_asin",
        title: "ASIN",
        filterKey: "product_asin",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 80,
        width: 100,
        dataIndex: "product_asin",
        renderCell: buildRenderAsinCell(amazonMarketplaces),
      },
      {
        key: "seller_sku",
        title: "SKU",
        filterKey: "seller_sku",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 80,
        width: 100,
        dataIndex: "seller_sku",
        renderCell: renderSellerSkuCell,
      },
      {
        key: "marketplace_id",
        title: l("Marketplace"),
        filterKey: "marketplace_id",
        sorter: true,
        type: "select",
        inputProps: {
          ...FILTER_BASE_INPUT_PROPS,
          options: marketplaceOptions,
          isContentMinWidthMatchedWithTrigger: true,
          isMultiSelect: true,
          maxWidth: 300,
        },
        minWidth: 80,
        width: 80,
        dataIndex: "marketplace_id",
        hasEllipsis: true,
        renderCell: buildGetOptionLabel(marketplaceOptions),
      },
      {
        key: "product_brand",
        title: l("Brand"),
        filterKey: "product_brand",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 120,
        width: 120,
        dataIndex: "product_brand",
        hasEllipsis: !shouldBlur,
        renderCell: buildRenderBlurredCell({
          renderValue: getCellValue,
          label: l("Brand"),
          shouldBlur,
        }),
      },
      {
        key: "product_type",
        title: l("Product Type"),
        filterKey: "product_type",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 120,
        width: 120,
        dataIndex: "product_type",
        hasEllipsis: !shouldBlur,
        renderCell: buildRenderBlurredCell({
          renderValue: getCellValue,
          label: l("Product Type"),
          shouldBlur,
        }),
      },
      {
        key: "product_manufacturer",
        title: l("Manufacturer"),
        filterKey: "product_manufacturer",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 120,
        width: 120,
        dataIndex: "product_manufacturer",
        hasEllipsis: true,
        renderCell: getCellValue,
      },
      {
        key: "units",
        title: l("Units"),
        filterKey: "units",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 120,
        width: 120,
        dataIndex: "units",
        hasEllipsis: true,
        renderCell: renderUnitsCell,
      },
      {
        key: "orders",
        title: l("Order items"),
        filterKey: "orders",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 120,
        width: 120,
        dataIndex: "orders",
        renderCell: renderOrdersCell,
      },
      {
        key: "refunds",
        title: l("Refunds"),
        filterKey: "refunds",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 120,
        width: 120,
        dataIndex: "refunds",
        hasEllipsis: !shouldBlur,
        renderCell: buildRenderBlurredCell({
          renderValue: renderRefundsCell,
          label: l("Refunds"),
          shouldBlur,
        }),
      },
      {
        key: "revenue_amount",
        title: l("Revenue"),
        filterKey: "revenue_amount",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 120,
        width: 120,
        dataIndex: "revenue_amount",
        renderCell: renderRevenueAmountCell,
      },
      {
        key: "estimated_profit_amount",
        title: l("Estimated margin"),
        filterKey: "estimated_profit_amount",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 120,
        width: 120,
        dataIndex: "estimated_profit_amount",
        renderCell: buildRenderEstimatedProfitAmountCell({
          currencyCode: currency_code,
        }),
      },
      {
        key: "total_income",
        title: l("Total income"),
        filterKey: "total_income",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 120,
        width: 120,
        dataIndex: "total_income",
        hasEllipsis: !shouldBlur,
        renderCell: buildRenderBlurredCell({
          renderValue: renderTotalIncomeCell,
          label: l("Total income"),
          shouldBlur,
        }),
      },
      {
        key: "expenses_amount",
        title: l("Total expenses"),
        filterKey: "expenses_amount",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 130,
        width: 130,
        dataIndex: "expenses_amount",
        hasEllipsis: !shouldBlur,
        renderCell: buildRenderBlurredCell({
          renderValue: renderExpensesAmountCell,
          label: l("Expenses"),
          shouldBlur,
        }),
      },
      {
        key: "ppc_costs",
        title: l("Ads (PPC)"),
        filterKey: "ppc_costs",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 120,
        width: 120,
        dataIndex: "ppc_costs",
        hasEllipsis: !shouldBlur,
        renderCell: buildRenderBlurredCell({
          renderValue: (params) => {
            return ln(params.value || 0, 2, {
              currency: currency_code,
            })
          },
          label: l("Ads (PPC)"),
          shouldBlur,
        }),
      },
      {
        key: "amazon_fees",
        title: l("Amazon fees"),
        filterKey: "amazon_fees",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 120,
        width: 120,
        dataIndex: "amazon_fees",
        hasEllipsis: !shouldBlur,
        renderCell: buildRenderBlurredCell({
          renderValue: renderAmazonFeesCell,
          label: l("Amazon fees"),
          shouldBlur,
        }),
      },
      {
        key: "margin",
        title: l("Estimated margin %"),
        filterKey: "margin",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 120,
        width: 120,
        dataIndex: "margin",
        hasEllipsis: !shouldBlur,
        renderCell: buildRenderBlurredCell({
          renderValue: getPercentValue,
          label: l("Estimated margin %"),
          shouldBlur,
        }),
      },
      {
        key: "roi",
        title: l("ROI"),
        filterKey: "roi",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 120,
        width: 120,
        dataIndex: "roi",
        hasEllipsis: !shouldBlur,
        renderCell: buildRenderBlurredCell({
          renderValue: getPercentValue,
          label: l("ROI"),
          shouldBlur,
        }),
      },

      {
        key: "markup",
        title: l("Markup"),
        filterKey: "markup",
        sorter: true,
        type: "text",
        inputProps: FILTER_BASE_INPUT_PROPS,
        minWidth: 120,
        width: 120,
        dataIndex: "markup",
        hasEllipsis: !shouldBlur,
        renderCell: buildRenderBlurredCell({
          renderValue: getPercentValue,
          label: l("Markup"),
          shouldBlur,
        }),
      },
      {
        key: "seller_id",
        title: l("Amazon account name"),
        filterKey: "seller_id",
        sorter: true,
        type: "select",
        inputProps: {
          ...FILTER_BASE_INPUT_PROPS,
          options: basAccountsOptions,
          isContentMinWidthMatchedWithTrigger: true,
          isMultiSelect: true,
          maxWidth: 300,
        },
        minWidth: 120,
        width: 120,
        dataIndex: "seller_id",
        hasEllipsis: !shouldBlur,
        renderCell: buildRenderSellerIdCell({
          amazonCustomerAccounts,
          shouldBlur,
        }),
      },
    ]
  }, [
    hasFreemiumRestrictions,
    getMarketplaceOptions,
    seller_id,
    from,
    to,
    view,
    minStatsDate,
    today,
    currency_code,
    amazonMarketplaces,
    basAccountsOptions,
    amazonCustomerAccounts,
    marketplace_id,
  ])
}
