import { useCallback, useMemo, useRef } from "react"
import { DeepPartial } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { useHistory } from "react-router-dom"
import type { TableSearchParams } from "@develop/fe-library"
import {
  getUrlSearchParams,
  removeNullAndUndefined,
} from "@develop/fe-library/dist/utils"

import { productAggregatedSalesInfoActions } from "actions/productAggregatedSalesInfoActions"
import { GetProductAggregatedSalesInfoTableDataParams } from "actions/productAggregatedSalesInfoActions/ProductAggregatedSalesInfoActionsTypes"

import { productAggregatedSalesInfoTableStatesSelector } from "selectors/productAggregatedSalesInfoSelectors/productAggregatedSalesInfoSelectors"

import type { ProductAggregatedSalesInfoTableFilterValues } from "components/ProductAggregatedSalesInfoTable/ProductAggregatedSalesInfoTableTypes"

import { useSellerMarketplaceParams } from "hooks"

import { checkIsArray } from "utils/arrayHelpers"

import { PRODUCT_AGGREGATED_SALES_INFO_TABLE_GROUP_BY } from "constants/productAggregatedSalesInfoTableGroupBy"
import {
  PAGE_DEFAULT,
  PAGE_SIZE_DEFAULT,
  SORT_DEFAULT,
} from "constants/tableSettingsNew"

import { ProductAggregatedSalesInfoTableGroupBy } from "types/ProductAggregatedSalesInfoTableGroupBy"

export const useSearch = ({
  pageSize,
  handleChangePageSize,
  expandedRows,
  handleCollapseAllRows,
}) => {
  const dispatch = useDispatch()
  const history = useHistory()

  const { data, totalCount, isLoading, expandedRowStatuses } = useSelector(
    productAggregatedSalesInfoTableStatesSelector,
  )

  const { getSellerMarketplaceParams } = useSellerMarketplaceParams()

  const filterDefaultValues: DeepPartial<ProductAggregatedSalesInfoTableFilterValues> =
    useMemo(() => {
      const searchParams = getUrlSearchParams({
        locationSearch: document.location.search,
      })

      const groupBy = searchParams["products-group_by"]

      return {
        page: PAGE_DEFAULT,
        pageSize: pageSize || PAGE_SIZE_DEFAULT,
        sort: "-units",
        group_by:
          PRODUCT_AGGREGATED_SALES_INFO_TABLE_GROUP_BY[groupBy] ||
          PRODUCT_AGGREGATED_SALES_INFO_TABLE_GROUP_BY.asin,
      }
    }, [pageSize])

  const groupByPreviousValue = useRef<
    ProductAggregatedSalesInfoTableGroupBy | undefined
  >(filterDefaultValues.group_by)

  const buildRequestParams = useCallback(
    (
      params: Partial<ProductAggregatedSalesInfoTableFilterValues>,
    ): GetProductAggregatedSalesInfoTableDataParams => {
      const { globalParams = {}, group_by, ...rest } = params

      const sellerId = rest.seller_id || globalParams.seller_id
      const marketplaceId = rest.marketplace_id
        ? rest.marketplace_id.join(",")
        : globalParams.marketplace_id

      const { seller_id, marketplace_id, marketplaceSellerIds } =
        getSellerMarketplaceParams({
          ...globalParams,
          seller_id: sellerId,
          marketplace_id: checkIsArray(marketplaceId)
            ? marketplaceId.join(",")
            : marketplaceId,
        })

      return {
        expandedRows,
        groupBy: group_by,
        requestParams: removeNullAndUndefined<
          GetProductAggregatedSalesInfoTableDataParams["requestParams"]
        >({
          page: PAGE_DEFAULT,
          pageSize: PAGE_SIZE_DEFAULT,
          sort: SORT_DEFAULT,
          ...rest,
          ...globalParams,
          product_asin: globalParams.product_asin || rest.product_asin,
          product_brand: globalParams.product_brand || rest.product_brand,
          product_manufacturer:
            globalParams.product_manufacturer || rest.product_manufacturer,
          product_type: globalParams.product_type || rest.product_type,
          seller_sku: globalParams.seller_sku || rest.seller_sku,
          seller_id,
          marketplace_seller_ids: marketplaceSellerIds,
          marketplace_id,
        }),
      }
    },
    [getSellerMarketplaceParams],
  )

  const handleSearch = useCallback(
    ({
      params,
      searchString,
    }: TableSearchParams<ProductAggregatedSalesInfoTableFilterValues>): void => {
      history.push({
        ...history.location,
        search: searchString,
      })

      const successCallback = (): void => {
        // Update the page size in table settings
        handleChangePageSize(params.pageSize)
      }

      const requestParams = buildRequestParams(params)

      if (requestParams.groupBy !== groupByPreviousValue.current) {
        handleCollapseAllRows()
        groupByPreviousValue.current = requestParams.groupBy
      }

      dispatch(
        productAggregatedSalesInfoActions.getProductAggregatedSalesInfoTableData(
          {
            params: requestParams,
            successCallback,
          },
        ),
      )
    },
    [buildRequestParams, handleChangePageSize, expandedRows],
  )

  return {
    data,
    totalCount,
    filterDefaultValues,
    handleSearch,
    isLoading,
    expandedRowStatuses,
    buildRequestParams,
  }
}
