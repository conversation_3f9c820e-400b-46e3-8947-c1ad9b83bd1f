import type { ReactNode } from "react"
import type { RenderCustomExpandedRowParams } from "@develop/fe-library"

import { GetProductAggregatedSalesInfoTableDataParams } from "actions/productAggregatedSalesInfoActions/ProductAggregatedSalesInfoActionsTypes"

import type {
  ProductAggregatedSalesInfoTableData,
  ProductAggregatedSalesInfoTableFilterValues,
} from "components/ProductAggregatedSalesInfoTable/ProductAggregatedSalesInfoTableTypes"

import type { AsyncStatus } from "types"

type UseRenderCustomExpandedRowParams = {
  columnsCount: number
  buildRequestParams: (
    filterValues: Partial<ProductAggregatedSalesInfoTableFilterValues>,
  ) => GetProductAggregatedSalesInfoTableDataParams
  expandedRowStatuses: Record<string, AsyncStatus>
  onExpand: (id: string) => void
}

type UseRenderCustomExpandedRowReturn = (
  params: RenderCustomExpandedRowParams<
    ProductAggregatedSalesInfoTableData,
    ProductAggregatedSalesInfoTableFilterValues
  >,
) => ReactNode

export type UseRenderCustomExpandedRow = (
  params: UseRenderCustomExpandedRowParams,
) => UseRenderCustomExpandedRowReturn
