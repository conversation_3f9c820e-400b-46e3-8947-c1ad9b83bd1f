import { useCallback } from "react"

import { CustomExpandedRow } from "components/ProductAggregatedSalesInfoTable/components"

import { UseRenderCustomExpandedRow } from "components/ProductAggregatedSalesInfoTable/hooks/useRenderCustomExpandedRow/UseRenderCustomExpandedRowTypes"

export const useRenderCustomExpandedRow: UseRenderCustomExpandedRow = ({
  columnsCount,
  buildRequestParams,
  expandedRowStatuses,
  onExpand,
}) => {
  return useCallback(
    ({ index, item, getFilterValues }) => {
      return (
        <CustomExpandedRow
          buildRequestParams={buildRequestParams}
          columnsCount={columnsCount}
          expandedRowStatuses={expandedRowStatuses}
          getFilterValues={getFilterValues}
          index={index}
          item={item}
          onExpand={onExpand}
        />
      )
    },
    [columnsCount, buildRequestParams, expandedRowStatuses, onExpand],
  )
}
