import { useCallback, useMemo, useState } from "react"

import { checkIsArray } from "utils/arrayHelpers"
import { getItem, setItem } from "utils/storage"

import { EXPANDED_ROW_PAGE_SIZE } from "constants/expandedRowPageSize"
import { SESSION_STORAGE_KEYS } from "constants/sessionStorage"

import type { UseExpandedRows } from "./UseExpandedRowsTypes"

export const useExpandedRows: UseExpandedRows = () => {
  const [expandedRows, setExpandedRows] = useState<Record<string, number>>(
    () => {
      const expandedRowIdsFromSessionStorage = getItem(
        SESSION_STORAGE_KEYS.DASHBOARD_PRODUCTS_EXPANDED_ROWS,
        true,
      )

      if (!expandedRowIdsFromSessionStorage) {
        return {}
      }

      const parsed: Record<string, number> = JSON.parse(
        expandedRowIdsFromSessionStorage,
      )

      return parsed
    },
  )

  const expandedRowIds = useMemo((): Set<string> => {
    const expandedRowIds = Object.keys(expandedRows)

    if (!checkIsArray(expandedRowIds)) {
      return new Set()
    }

    return new Set(expandedRowIds)
  }, [expandedRows])

  const handleExpand = useCallback((id: string) => {
    setExpandedRows((prevExpandedRows) => {
      const expandedRowCountOld = prevExpandedRows[id] || 0
      const expandedRowCountNew = expandedRowCountOld + EXPANDED_ROW_PAGE_SIZE

      const newExpandedRows = {
        ...prevExpandedRows,
        [id]: expandedRowCountNew,
      }

      setItem(
        SESSION_STORAGE_KEYS.DASHBOARD_PRODUCTS_EXPANDED_ROWS,
        JSON.stringify(newExpandedRows),
        true,
      )

      return newExpandedRows
    })
  }, [])

  const handleCollapse = useCallback((id: string) => {
    setExpandedRows((prevExpandedRows) => {
      const { [id]: collapsedRow, ...rest } = prevExpandedRows

      setItem(
        SESSION_STORAGE_KEYS.DASHBOARD_PRODUCTS_EXPANDED_ROWS,
        JSON.stringify(rest),
        true,
      )

      return rest
    })
  }, [])

  const handleCollapseAllRows = useCallback(() => {
    setItem(
      SESSION_STORAGE_KEYS.DASHBOARD_PRODUCTS_EXPANDED_ROWS,
      JSON.stringify({}),
      true,
    )

    setExpandedRows({})
  }, [])

  return {
    expandedRows,
    expandedRowIds,
    handleExpand,
    handleCollapse,
    handleCollapseAllRows,
  }
}
