import React, { useMemo } from "react"
import { useSelector } from "react-redux"
import { Box, CommonMessage, Table } from "@develop/fe-library"

import { staffCurrentUserRoleSelector } from "selectors/mainStateSelectors"

import { TABLE_MAIN_BOX_PROPS } from "components/ProductAggregatedSalesInfoTable/constants"
import {
  normalizeFilterValuesToUrl,
  parseFilterValuesFromUrl,
} from "components/ProductAggregatedSalesInfoTable/utils"

import { buildTableLabels } from "utils/buildTableLabels"
import l from "utils/intl"

import { PRODUCTS_AGGREGATED_SALES_INFO_TABLE_SETTINGS_KEY } from "constants/productAggregatedSalesInfo"
import { defaultSettings } from "constants/tableSettingsNew"
import { USER_ROLES } from "constants/user"

import {
  useActionColumn,
  useColumns,
  useExpandedRows,
  useRenderCustomExpandedRow,
  useSearch,
  useTableSettings,
} from "../../hooks"

import type {
  ProductAggregatedSalesInfoTableData,
  ProductAggregatedSalesInfoTableFilterValues,
  ProductAggregatedSalesInfoTableProps,
} from "../../ProductAggregatedSalesInfoTableTypes"

export const TableComponent = ({
  urlSearchDefaultValue,
}: ProductAggregatedSalesInfoTableProps) => {
  const role = useSelector(staffCurrentUserRoleSelector)

  const { settings, handleChangeSettings, pageSize, handleChangePageSize } =
    useTableSettings()

  const columns = useColumns()

  const {
    expandedRows,
    expandedRowIds,
    handleExpand,
    handleCollapse,
    handleCollapseAllRows,
  } = useExpandedRows()

  const {
    data,
    totalCount,
    filterDefaultValues,
    handleSearch,
    isLoading,
    expandedRowStatuses,
    buildRequestParams,
  } = useSearch({
    pageSize,
    handleChangePageSize,
    expandedRows,
    handleCollapseAllRows,
  })

  const renderCustomExpandedRow = useRenderCustomExpandedRow({
    columnsCount: columns.length,
    buildRequestParams,
    expandedRowStatuses,
    onExpand: handleExpand,
  })

  const actionColumn = useActionColumn({
    buildRequestParams,
    onExpand: handleExpand,
    onCollapse: handleCollapse,
    expandedRowStatuses,
  })

  const labels = useMemo(buildTableLabels, [])

  // Save expanded rows state on filter change and page reload / session storage
  // Lock group by column as first
  // Keep large cell white non-clickable
  // Set isLoading only when all data is reloaded. When expanded row is reloaded, show skeletons only in load more row

  return (
    <Table<
      ProductAggregatedSalesInfoTableData,
      ProductAggregatedSalesInfoTableFilterValues
    >
      hasStickyHeader
      isExpandable
      actionColumn={actionColumn}
      bordersVariant="complete"
      canSaveAsDefaultSettings={role === USER_ROLES.superAdmin}
      columns={columns}
      data={data}
      expandedRowIds={expandedRowIds}
      filterDefaultValues={filterDefaultValues}
      filtersPrefix="products"
      isLoading={isLoading}
      labels={labels}
      mainBoxProps={TABLE_MAIN_BOX_PROPS}
      normalizeFilterValuesToUrl={normalizeFilterValuesToUrl}
      parseFilterValuesFromUrl={parseFilterValuesFromUrl}
      renderCustomExpandedRow={renderCustomExpandedRow}
      settings={settings}
      totalCount={totalCount}
      urlSearchDefaultValue={urlSearchDefaultValue}
      variant="standalone"
      noDataPlaceholder={
        <Box height={300} justify="center" width="100%">
          <CommonMessage icon="icnInbox" title={l("No data")} />
        </Box>
      }
      tableContentDefaultSettings={
        defaultSettings[PRODUCTS_AGGREGATED_SALES_INFO_TABLE_SETTINGS_KEY]
      }
      onChangeSettings={handleChangeSettings}
      onSearch={handleSearch}
    />
  )
}
