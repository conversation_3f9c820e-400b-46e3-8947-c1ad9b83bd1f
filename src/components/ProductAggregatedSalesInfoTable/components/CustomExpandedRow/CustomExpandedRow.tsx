import React, { useCallback } from "react"
import { useDispatch } from "react-redux"
import { <PERSON>, But<PERSON>, Spinner } from "@develop/fe-library"

import { productAggregatedSalesInfoActions } from "actions/productAggregatedSalesInfoActions"
import { GetProductAggregatedSalesInfoTableDataParams } from "actions/productAggregatedSalesInfoActions/ProductAggregatedSalesInfoActionsTypes"

import type { CustomExpandedRowParams } from "components/ProductAggregatedSalesInfoTable/components/CustomExpandedRow/CustomExpandedRowTypes"

import { buildProductAggregatedSalesInfoTableSpecialParams } from "utils/buildProductAggregatedSalesInfoTableSpecialParams"
import l from "utils/intl"

import { ASYNC_STATUSES } from "constants/async"
import { EXPANDED_ROW_PAGE_SIZE } from "constants/expandedRowPageSize"

export const CustomExpandedRow = ({
  columnsCount,
  index,
  item,
  getFilterValues,
  buildRequestParams,
  expandedRowStatuses,
  onExpand,
}: CustomExpandedRowParams) => {
  const dispatch = useDispatch()

  const expandedParentRowId = String(item.tableRowMetaData?.expandedParentRowId)

  const total: number = item.group_items_count
    ? parseInt(item.group_items_count, 10)
    : 0

  const loaded: number = item.children?.length || 0

  const remaining = total - loaded

  const handleLoadMore = useCallback(() => {
    if (!expandedParentRowId) {
      return
    }

    const page =
      loaded > EXPANDED_ROW_PAGE_SIZE
        ? Math.ceil(loaded / EXPANDED_ROW_PAGE_SIZE) + 1
        : 2

    const filterValues = getFilterValues?.()

    if (!filterValues.group_by || !item.group_value) {
      return
    }

    onExpand(expandedParentRowId)

    const requestParams = buildRequestParams(filterValues)

    const requestParamsProcessed: GetProductAggregatedSalesInfoTableDataParams["requestParams"] =
      {
        ...requestParams.requestParams,
        ...buildProductAggregatedSalesInfoTableSpecialParams({
          groupBy: filterValues.group_by,
          groupValue: item.group_value,
          requestParams: requestParams.requestParams,
        }),
      }

    dispatch(
      productAggregatedSalesInfoActions.expandRow({
        params: {
          id: expandedParentRowId,
          page,
          requestParams: requestParamsProcessed,
        },
      }),
    )
  }, [
    expandedParentRowId,
    loaded,
    getFilterValues,
    item.group_value,
    onExpand,
    buildRequestParams,
    dispatch,
  ])

  if (!remaining) {
    return null
  }

  console.log(expandedRowStatuses)

  return (
    <tr>
      <td
        colSpan={columnsCount - 1}
        style={{
          borderLeft: "var(--border-main)",
        }}
      >
        {expandedRowStatuses[expandedParentRowId] === ASYNC_STATUSES.PENDING ? (
          <Box gap="s" padding="m">
            <Spinner type="circle" />
            {l("Loading...")}
          </Box>
        ) : (
          <Box padding="m">
            <Button icon="icnArrowDown" onClick={handleLoadMore}>
              {l("Load more")}
            </Button>
          </Box>
        )}
      </td>
    </tr>
  )
}
