import type { GetProductAggregatedSalesInfoTableDataParams } from "actions/productAggregatedSalesInfoActions/ProductAggregatedSalesInfoActionsTypes"

import type {
  ProductAggregatedSalesInfoTableData,
  ProductAggregatedSalesInfoTableFilterValues,
} from "components/ProductAggregatedSalesInfoTable/ProductAggregatedSalesInfoTableTypes"

import type { AsyncStatus } from "types"

export type CustomExpandedRowParams = {
  columnsCount: number
  index: number
  item: ProductAggregatedSalesInfoTableData
  getFilterValues: () => ProductAggregatedSalesInfoTableFilterValues
  buildRequestParams: (
    filterValues: Partial<ProductAggregatedSalesInfoTableFilterValues>,
  ) => GetProductAggregatedSalesInfoTableDataParams
  expandedRowStatuses: Record<string, AsyncStatus>
  onExpand: (id: string) => void
}
