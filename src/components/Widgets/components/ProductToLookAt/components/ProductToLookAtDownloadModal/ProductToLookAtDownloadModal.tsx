import React from "react"
import { <PERSON>, Button, Modal, Select, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { useProductToLookAtDownloadModal } from "./hooks"

import { DOWNLOAD_OPTIONS } from "./hooks/useProductToLookAtDownloadModal/constants"

import { ProductToLookAtDownloadModalProps } from "./ProductToLookAtDownloadModalTypes"

export const ProductToLookAtDownloadModal = ({
  isVisible,
  onCancel,
  chartRef,
}: ProductToLookAtDownloadModalProps) => {
  const { fileFormat, handleFormatChange, handleDownload, visible } =
    useProductToLookAtDownloadModal({
      isVisible,
      onCancel,
      chartRef,
    })

  return (
    <Modal
      title={l("Download chart")}
      visible={visible}
      footer={
        <Box gap="m" justify="flex-end" width="100%">
          <Button variant="secondary" onClick={onCancel}>
            {l("Cancel")}
          </Button>
          <Button onClick={handleDownload}>{l("Download")}</Button>
        </Box>
      }
      onCancel={onCancel}
      onOk={handleDownload}
    >
      <Box flexDirection="column" gap="l">
        <Typography color="--color-text-main" variant="--font-body-text-7">
          {l(
            "Choose how you want to download your Negative margin products chart",
          )}
        </Typography>

        <Select
          defaultValue={fileFormat}
          options={DOWNLOAD_OPTIONS}
          onChange={handleFormatChange}
        />
      </Box>
    </Modal>
  )
}
