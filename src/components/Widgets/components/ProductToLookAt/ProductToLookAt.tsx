import React from "react"
import withSizes from "react-sizes"
import {
  Bar,
  ComposedChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"
import { Box, IconPopover, Skeleton, Typography } from "@develop/fe-library"

import { SetupWizardLink } from "components/shared/SetupWizardLink"
import { GenericWidget } from "components/shared/Widget/components/GenericWidget"

import l from "utils/intl"

import {
  ProductToLookAtCustomBar,
  ProductToLookAtCustomYAxisTick,
  ProductToLookAtExportModal,
  ProductToLookAtFullscreenModal,
} from "./components"

import { useProductToLookAt } from "./hooks"

import { ProductToLookAtDownloadModal } from "./components/ProductToLookAtDownloadModal"

import { ProductToLookAtProps } from "./ProductToLookAtTypes"

export const ProductToLookAtComponent = ({
  resizeKey,
}: ProductToLookAtProps) => {
  const {
    options,
    handleSelectOption,

    chartRef,
    isWidgetLoading,
    isWidgetReady,
    isWidgetNoData,
    widgetsProductAggregatedSalesInfoData,
    barChartHeight,
    iconPopoverContent,
    headerTitle,

    tooltipProductId,

    isExportModalOpen,
    handleOpenExportModal,
    handleCloseExportModal,

    downloadModalOpen,
    isDownloadModalOpen,
    handleOpenDownloadModal,
    handleCloseDownloadModal,

    isFullscreen,
    handleCloseFullscreen,
    handleOpenFullscreen,

    urlParams,
    isMobile,
    isWidgetAvailable,
    customTooltip,
  } = useProductToLookAt()

  return (
    <>
      <GenericWidget
        hasFullscreen
        managePermission={isWidgetAvailable}
        options={options}
        restrictedPopoverMessage={<SetupWizardLink />}
        containerProps={{
          marginBottom: "l",
        }}
        content={
          <Box align="center" gap="s">
            <Typography variant="--font-body-text-2">{headerTitle}</Typography>

            {iconPopoverContent ? (
              <IconPopover
                color="--color-icon-active"
                content={iconPopoverContent}
                name="icnInfoCircle"
                placement="top"
                size="--icon-size-3"
              />
            ) : null}
          </Box>
        }
        onFullscreen={handleOpenFullscreen}
        onSelectOption={handleSelectOption}
      >
        <Box
          display="block"
          height={500}
          maxHeight={500}
          padding="m"
          width="100%"
          style={{
            overflowY: "auto",
          }}
          tb={{
            padding: "l",
            maxHeight: 310,
          }}
        >
          {!isWidgetAvailable ? (
            <Box align="center" height="100%" justify="center">
              <SetupWizardLink />
            </Box>
          ) : (
            <>
              {isWidgetLoading ? (
                <Box display="block">
                  <Skeleton
                    count={8}
                    height="16px"
                    width="100%"
                    style={{
                      marginBottom: isMobile ? 34 : 14,
                      lineHeight: "16px",
                    }}
                  />
                </Box>
              ) : null}
              {isWidgetNoData ? (
                <Box
                  align="center"
                  flexDirection="column"
                  height="100%"
                  justify="center"
                  width="100%"
                >
                  <Typography variant="--font-headline-3">
                    {l("No data found for the selected period")}
                  </Typography>
                  <Typography
                    color="--color-text-second"
                    variant="--font-body-text-4"
                  >
                    {l(
                      "Please select another period, another marketplace, or wait for SELLERLOGIC to synchronize with Amazon.",
                    )}
                  </Typography>
                </Box>
              ) : null}

              {isWidgetReady ? (
                <Box ref={chartRef} display="block">
                  <ResponsiveContainer
                    key={resizeKey}
                    height={barChartHeight}
                    width="100%"
                  >
                    <ComposedChart
                      barCategoryGap="20%"
                      barGap={20}
                      data={widgetsProductAggregatedSalesInfoData}
                      layout="vertical"
                      margin={{
                        top: 0,
                        right: isMobile ? 0 : 140,
                        left: 0,
                        bottom: 0,
                      }}
                    >
                      <XAxis
                        hide
                        axisLine={false}
                        dataKey="expenses_amount"
                        height={50}
                        tickLine={false}
                        type="number"
                      />
                      <YAxis
                        axisLine={false}
                        dataKey="index"
                        tickLine={false}
                        type="category"
                        width={26}
                        tick={ProductToLookAtCustomYAxisTick({
                          isMobile,
                        })}
                      />

                      <Bar
                        dataKey="expenses_amount"
                        stroke="#3b5cd2"
                        strokeWidth={1}
                        shape={ProductToLookAtCustomBar({
                          tooltipProductId,
                          currency_code: urlParams.currency_code,
                          isMobile,
                        })}
                      />

                      <Tooltip
                        content={customTooltip}
                        position={{ x: 0 }}
                        cursor={{
                          strokeWidth: 0,
                        }}
                      />
                    </ComposedChart>
                  </ResponsiveContainer>
                </Box>
              ) : null}
            </>
          )}
        </Box>
      </GenericWidget>
      {isExportModalOpen ? (
        <ProductToLookAtExportModal
          isVisible={isExportModalOpen}
          onCancel={handleCloseExportModal}
        />
      ) : null}
      {isDownloadModalOpen ? (
        <ProductToLookAtDownloadModal
          chartRef={downloadModalOpen}
          isVisible={isDownloadModalOpen}
          onCancel={handleCloseDownloadModal}
        />
      ) : null}

      {isFullscreen && isWidgetAvailable ? (
        <ProductToLookAtFullscreenModal
          isVisible
          title={headerTitle}
          onCancel={handleCloseFullscreen}
          onOpenDownloadModal={handleOpenDownloadModal}
          onOpenExportModal={handleOpenExportModal}
        />
      ) : null}
    </>
  )
}

const mapSizesToProps = ({ width }: { width: number }) => ({
  resizeKey: `ptla-${width}`,
})

export const ProductToLookAt = withSizes(mapSizesToProps)(
  ProductToLookAtComponent,
)
