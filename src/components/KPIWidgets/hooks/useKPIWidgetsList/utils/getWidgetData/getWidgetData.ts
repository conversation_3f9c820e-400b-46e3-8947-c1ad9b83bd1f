import { InputMode } from "@develop/fe-library"

import { KPIWIDGET_LIST_DEFAULT } from "components/KPIWidgets/constants"

import { INPUT_MODE } from "constants/dateRange"
import { KPI_WIDGET_LIST } from "constants/kpiWidgets"

import {
  GetWidgetDataParams,
  GetWidgetDataReturnType,
} from "./getWidgetDataTypes"

export const getWidgetData = ({
  selectedWidgetIndex,
  sessionKPIWidgetsSettings,
  urlParams,
}: GetWidgetDataParams): GetWidgetDataReturnType => {
  let nextSelectedWidgetIndex: number =
    selectedWidgetIndex ?? Number(KPI_WIDGET_LIST.THIRD)

  let defaultInputModeList: InputMode[] = KPIWIDGET_LIST_DEFAULT

  if (typeof selectedWidgetIndex === "number") {
    // At this point we rely on sessionKPIWidgetsSettings
    const sessionInputMode: InputMode | undefined =
      sessionKPIWidgetsSettings?.[selectedWidgetIndex]?.inputMode
    const isSessionInputMode: boolean = !!sessionInputMode

    // 1. If urlParams.inputMode none or is the same as saved
    const shouldUseSessionWidgets: boolean =
      !urlParams.inputMode ||
      (isSessionInputMode && urlParams.inputMode === sessionInputMode)

    // 2. If urlParams.inputMode doesn't the same as saved
    const shouldSetAndSelectFirstWidget: boolean =
      !!urlParams.inputMode &&
      isSessionInputMode &&
      urlParams.inputMode !== sessionInputMode

    if (shouldUseSessionWidgets) {
      nextSelectedWidgetIndex = selectedWidgetIndex
      defaultInputModeList = sessionKPIWidgetsSettings.map(
        (setting) => setting.inputMode,
      )
    } else if (shouldSetAndSelectFirstWidget) {
      nextSelectedWidgetIndex = Number(KPI_WIDGET_LIST.FIRST)
      defaultInputModeList = [
        urlParams.inputMode as InputMode,
        ...sessionKPIWidgetsSettings
          .slice(1)
          .map((setting) => setting.inputMode),
      ]
    }
  } else {
    // At this point we do not rely on sessionKPIWidgetsSettings
    // 3. If urlParams.inputMode none or currentMonth
    const isDefault: boolean =
      !urlParams.inputMode || urlParams.inputMode === INPUT_MODE.CURRENT_MONTH

    // 4. If urlParams.inputMode have other than currentMonth
    const shouldSetAndSelectFirstWidget: boolean =
      !!urlParams.inputMode && urlParams.inputMode !== INPUT_MODE.CURRENT_MONTH

    if (isDefault) {
      nextSelectedWidgetIndex = Number(KPI_WIDGET_LIST.THIRD)
      defaultInputModeList = KPIWIDGET_LIST_DEFAULT
    } else if (shouldSetAndSelectFirstWidget) {
      nextSelectedWidgetIndex = Number(KPI_WIDGET_LIST.FIRST)
      defaultInputModeList = [
        urlParams.inputMode as InputMode,
        ...KPIWIDGET_LIST_DEFAULT.slice(1),
      ]
    }
  }

  return {
    nextSelectedWidgetIndex,
    defaultInputModeList,
  }
}
