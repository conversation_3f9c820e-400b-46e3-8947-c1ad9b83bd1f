import React from "react"
import { Box, Typography } from "@develop/fe-library"

import { Link } from "components/KPIWidgets/components"

import { useUrlParams } from "hooks"

import { formatWidgetValue } from "utils/formatWidgetValue"
import l from "utils/intl"

import { useMetricUrl } from "../../hooks"

import { DashboardFiltersParams } from "types"

import { ProfitBreakdownCategoriesProps } from "./ProfitBreakdownCategoriesTypes"

export const ProfitBreakdownCategories = ({
  categories,
  dates,
  inputMode,
}: ProfitBreakdownCategoriesProps) => {
  const { urlParams } = useUrlParams<DashboardFiltersParams>()

  const { getMetricUrl } = useMetricUrl({
    urlParams,
    selectedDates: dates,
    inputMode,
  })

  return (
    <Box
      columnGap="l"
      display="grid"
      flexDirection="column"
      gridTemplateColumns="repeat(2, 1fr)"
      rowGap="m"
    >
      {categories.map((item) => {
        return (
          <Box key={item.id} gap="m" justify="space-between">
            <Box>
              <Typography variant="--font-body-text-9" wordBreak="break-all">
                {item.name ? l(item.name) : ""}
              </Typography>
            </Box>

            <Box>
              <Typography variant="--font-body-text-9" whiteSpace="nowrap">
                <Link
                  url={getMetricUrl({
                    sales_category_depth_1: item.id ? [item.id] : undefined,
                  })}
                >
                  {formatWidgetValue({
                    value: item.amount,
                    type: item.type,
                    currency: urlParams.currency_code,
                  })}
                </Link>
              </Typography>
            </Box>
          </Box>
        )
      })}
    </Box>
  )
}
