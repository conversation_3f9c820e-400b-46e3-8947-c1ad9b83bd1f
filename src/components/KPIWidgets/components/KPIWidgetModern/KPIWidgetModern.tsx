import React from "react"
import {
  Box,
  DateRangePicker,
  Icon,
  IconPopover,
  Skeleton,
  Typography,
} from "@develop/fe-library"

import FormattedMessage from "components/FormattedMessage"
import { RestrictedIconPopover } from "components/shared/RestrictedIconPopover"
import { SetupWizardLink } from "components/shared/SetupWizardLink"

import l from "utils/intl"

import { LOCALES } from "constants/locales"

import { MainMetrics, ProfitBreakdown, SecondaryMetrics } from "./components"

import { useKPIWidgetModern, useKPIWidgetModernUI } from "./hooks"

import { KPIWidgetModernProps } from "./KPIWidgetModernTypes"

import styles from "./kpiWidgetModern.module.scss"

export const KPIWidgetModern = ({
  index,
  isOpenPopover,
  onOpenPopover,
  onClosePopover,
  isSelected,
  onSelectWidget,
}: KPIWidgetModernProps) => {
  const {
    widgetData,
    dateFormatted,
    title,
    handleDateRangeSelect,
    iconPopoverContent,
    isLoadingWidget,
    fromDate,
    toDate,
    selected,
    inputMode,
    language,
    locale,
    dateRangePickerLabels,
    isDisabledDateRangePicker,
    handleSelectWidget,
    hasConnectedAmazonAdAccounts,
    isDateRangePickerReady,
    isDisabled,
    isBasSubscriptionActive,
    isSelectedWidget,
  } = useKPIWidgetModern({
    index,
    onSelectWidget,
  })

  const { ref, handleTogglePopover, getBoxShadow } = useKPIWidgetModernUI({
    isOpenPopover,
    onClosePopover,
    onOpenPopover,
    index,
    isSelected,
  })

  // TODO: Move to ui library in https://sellerlogic.atlassian.net/browse/BAS-2188
  const ServiceColorRowSelect = "#e5effa"

  return (
    <Box
      ref={ref}
      hasBorder
      borderColor={isSelected ? "--color-border-active" : "--color-border-main"}
      borderRadius="--border-radius"
      boxShadow={getBoxShadow()}
      display="block"
      width="100%"
      zIndex={isOpenPopover ? 3 : undefined}
    >
      {/* Card */}
      <Box
        height="100%"
        position="relative"
        mSM={{
          backgroundColor: isSelected
            ? ServiceColorRowSelect
            : "var(--color-main-background)",
        }}
      >
        {/* Inner */}
        <Box
          display="grid"
          flexDirection="column"
          gridTemplateRows="auto 1fr 32px"
          width="100%"
        >
          {/* Header */}
          <Box
            align="center"
            component="header"
            gap="m"
            justify="space-between"
            padding="m"
            width="100%"
            hasBorder={{
              bottom: true,
            }}
            mXL={{
              padding: "m l",
            }}
          >
            <Box align="center" columnGap="s" flexGrow={1} flexWrap="wrap">
              {isDateRangePickerReady ? (
                <>
                  {title ? (
                    <Box maxWidth="calc(100% - 21px)">
                      <Typography variant="--font-body-text-2">
                        <FormattedMessage id={title} />:
                      </Typography>
                    </Box>
                  ) : null}
                  <Box align="center" gap="s">
                    <Typography variant="--font-body-text-2">
                      {dateFormatted}
                    </Typography>
                    {iconPopoverContent ? (
                      <IconPopover
                        color="--color-icon-active"
                        content={iconPopoverContent}
                        name="icnInfoCircle"
                        placement="top"
                        size="--icon-size-3"
                      />
                    ) : null}
                  </Box>
                </>
              ) : (
                <Skeleton width="20ch" />
              )}
            </Box>

            <Box align="center" gap="m">
              {!isLoadingWidget ? (
                <DateRangePicker
                  isGlobal
                  fromDate={fromDate}
                  inputMode={inputMode}
                  isDisabled={isDisabledDateRangePicker}
                  labels={dateRangePickerLabels}
                  language={LOCALES[language]}
                  locale={LOCALES[locale]}
                  selected={selected}
                  toDate={toDate}
                  onSelect={handleDateRangeSelect}
                >
                  <Icon
                    color="--color-icon-static"
                    isDisabled={isDisabledDateRangePicker}
                    name="icnCalendar"
                    size="--icon-size-5"
                  />
                </DateRangePicker>
              ) : (
                <Skeleton width="20px" />
              )}
            </Box>
          </Box>

          {/* Content */}
          <Box
            flex={1}
            justify="space-between"
            padding="m"
            position="relative"
            mXL={{
              padding: "l",
            }}
          >
            {/* Click box */}
            {!isSelectedWidget ? (
              <Box
                bottom={0}
                cursor="pointer"
                left={0}
                position="absolute"
                right={0}
                top={0}
                zIndex={1}
                onClick={handleSelectWidget}
              />
            ) : null}

            {/* Main Metrics */}
            <Box
              flexDirection="column"
              flexGrow={1}
              gap="m"
              justify="space-between"
              maxHeight={hasConnectedAmazonAdAccounts ? "100%" : "120px"}
            >
              <MainMetrics data={widgetData} isLoading={isLoadingWidget} />
            </Box>

            <Box
              flexDirection="column"
              flexGrow={1}
              gap="m"
              justify="space-between"
              marginLeft="m"
              maxWidth="45%"
              mXL={{
                marginLeft: "l",
              }}
            >
              <SecondaryMetrics
                data={widgetData}
                dates={selected}
                index={index}
                inputMode={inputMode}
                isLoading={isLoadingWidget}
                onProfitBreakdownOpen={onOpenPopover}
              />
            </Box>
          </Box>

          {/* Footer */}
          <Box
            align="center"
            className={styles.footer}
            component="footer"
            cursor={isDisabled ? "default" : "pointer"}
            gap="m"
            justify="space-between"
            padding="0 m"
            width="100%"
            hasBorder={{
              top: true,
            }}
            mXL={{
              padding: "0 l",
            }}
            onClick={isDisabled ? undefined : handleTogglePopover}
          >
            <Typography variant="--font-body-text-6">
              {l("Profit breakdown")}
            </Typography>
            <Box transform={isOpenPopover ? "rotate(180deg)" : undefined}>
              <RestrictedIconPopover
                managePermission={isBasSubscriptionActive}
                name="icnChevronDown"
                popoverMessage={<SetupWizardLink />}
                size="--icon-size-5"
              />
            </Box>
          </Box>
        </Box>

        {/* Popover */}
        {isOpenPopover ? (
          <Box
            hasBorder
            backgroundColor="--color-background-second"
            borderTopColor="--color-border-main"
            boxShadow={getBoxShadow()}
            className={styles.profitBreakdown}
            gap="m"
            left={0}
            padding="m"
            position="absolute"
            right={0}
            top="100%"
            width="calc(100% + 2px)" // Add 2px (border left and right width) to the width to wide container
            borderColor={
              isSelected ? "--color-border-active" : "--color-border-main"
            }
            mSM={{
              backgroundColor: isSelected
                ? ServiceColorRowSelect
                : "var(--color-background-second)",
            }}
            mXL={{
              padding: "l",
            }}
          >
            <ProfitBreakdown
              data={widgetData}
              dates={selected}
              inputMode={inputMode}
            />
          </Box>
        ) : null}
      </Box>
    </Box>
  )
}
