import React from "react"
import PropTypes from "prop-types"
import { Field } from "formik"
import { Select } from "antd"

import withOutlineLabel from "components/hocs/withOutlineLabel"
import { SelectField, withErrorField } from "components/shared/FormFields"

import l from "utils/intl"
import { checkIsArray } from "utils/arrayHelpers"

import styles from "./recurrentSettingModal.module.scss"

const { Option } = Select

const SelectFieldWithError = withOutlineLabel(
  withErrorField(SelectField, false, false),
  "select",
)

export const RecurrentExportSettingFormFields = ({
  templates,
  hasExportTemplates,
}) => {
  const fieldName = hasExportTemplates ? "template_id" : "output_file_format"

  return !checkIsArray(templates) ? null : (
    <div className={styles.fieldsWrapper}>
      <Field
        name={fieldName}
        component={SelectFieldWithError}
        className={styles.field}
        placeholder={l("Template")}
      >
        {templates.map(({ id, format, title }) => (
          <Option key={id} value={id}>
            {title} ({format})
          </Option>
        ))}
      </Field>
      <div className={styles.separator} />
    </div>
  )
}

RecurrentExportSettingFormFields.propTypes = {
  templates: PropTypes.array.isRequired,
  hasExportTemplates: PropTypes.bool,
}

RecurrentExportSettingFormFields.defaultProps = {
  hasExportTemplates: false,
}
