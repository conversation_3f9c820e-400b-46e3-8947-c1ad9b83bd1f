import React from "react"
import { Tag } from "antd"
import { Popover } from "@develop/fe-library"
import { getObjectKeys } from "@develop/fe-library/dist/utils"
import PropTypes from "prop-types"

import { checkIsArray } from "utils/arrayHelpers"
import { summarizeBulkProcessingErrorItemErrors } from "utils/bulkHelper"

import styles from "./bulkProcessingErrorItem.module.scss"

const BulkProcessingErrorItem = ({ id, errors, onClick }) => {
  if (!checkIsArray(errors)) {
    return null
  }

  return (
    <span className={styles.warningItem}>
      <Popover
        contentClassName={styles.popoverBox}
        placement="topLeft"
        content={
          <>
            {getObjectKeys(errors).map((key, index) => (
              <div key={errors[key]}>{`${index + 1}. ${errors[key]}`}</div>
            ))}
          </>
        }
      >
        {/* eslint-disable-next-line */}
        <a onClick={onClick}>{`${id} `}</a>
        <Tag color="red">{summarizeBulkProcessingErrorItemErrors(errors)}</Tag>
      </Popover>
    </span>
  )
}

BulkProcessingErrorItem.propTypes = {
  id: PropTypes.number.isRequired,
  errors: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired,
}

export { BulkProcessingErrorItem }
