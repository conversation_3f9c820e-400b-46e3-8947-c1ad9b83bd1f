import React from "react"
import { <PERSON>, <PERSON><PERSON>, Spinner, Typography } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import Link from "components/shared/Link"

import l from "utils/intl"

import { useDeleteTagModal } from "./hooks"

export const DeleteTagModal = () => {
  const {
    hasProductsWithTags,
    initialValues,
    productTotalCount,
    handleModalCancel,
    handleModalOk,
    isLoading,
    isDeleteProductTagModalVisible,
  } = useDeleteTagModal()

  if (!isDeleteProductTagModalVisible) {
    return null
  }

  const productTagsText = hasProductsWithTags ? (
    <>
      {l("This tag is assigned to {following_products} products.", {
        following_products: (
          <Link
            internal
            styleType="primary"
            target="_blank"
            text={productTotalCount}
            type="span"
            variant="text"
            url={`${
              ROUTES.BAS_ROUTES.PATH_BAS_PRODUCT_COSTS
            }${getUrlSearchParamsString({
              params: { tag_id: initialValues?.id },
            })}`}
          />
        ),
      })}
      <br />
    </>
  ) : null

  return (
    <Modal
      visible
      cancelButtonProps={{ disabled: isLoading }}
      cancelButtonText={l("Cancel")}
      okButtonProps={{ disabled: isLoading }}
      okButtonText={l("Delete")}
      title={l("Delete Tag")}
      onCancel={handleModalCancel}
      onOk={handleModalOk}
    >
      {isLoading ? (
        <Box justify="center">
          <Spinner size="lg" />
        </Box>
      ) : (
        <Typography color="--color-text-second" variant="--font-body-text-7">
          {productTagsText}
          {l("Are you sure you want to delete it?")}
        </Typography>
      )}
    </Modal>
  )
}
