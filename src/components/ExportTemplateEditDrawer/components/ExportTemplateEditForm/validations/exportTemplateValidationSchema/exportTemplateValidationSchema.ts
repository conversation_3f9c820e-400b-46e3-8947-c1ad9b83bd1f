import * as yup from "yup"

import { buildErrorMessagesMapper } from "utils/formHelpers"
import l from "utils/intl"

import {
  CRITERIA_FIELDS_KEYS,
  FIELDS_KEYS,
  TEMPLATE_HANDLER_NAMES,
} from "constants/exportTemplates"

import { CreateValidationSchemaProps } from "./exportTemplateValidationSchemaTypes"

const { required: requiredErrorMessage } = buildErrorMessagesMapper()

export const createExportTemplateValidationSchema = ({
  exportTemplatesTitles,
}: CreateValidationSchemaProps) =>
  yup.object().shape({
    [FIELDS_KEYS.TITLE]: yup
      .string()
      .required(requiredErrorMessage)
      .test(
        "uniqueTitle",
        l("Such template name has been already taken"),
        (value) => {
          if (!value) {
            return false
          }

          return !exportTemplatesTitles.includes(value)
        },
      ),
    [FIELDS_KEYS.FORMAT]: yup.string().required(requiredErrorMessage),
    [FIELDS_KEYS.HANDLER_NAME]: yup.string().required(requiredErrorMessage),
    [FIELDS_KEYS.FIELDS]: yup
      .array()
      .nullable()
      .when(FIELDS_KEYS.HANDLER_NAME, {
        is: TEMPLATE_HANDLER_NAMES.ORDERS,
        then: (schema) => schema.min(1, requiredErrorMessage),
        otherwise: (schema) => schema,
      }),
    [FIELDS_KEYS.CRITERIA]: yup.object().when(FIELDS_KEYS.HANDLER_NAME, {
      is: TEMPLATE_HANDLER_NAMES.ORDERS,
      then: (schema) =>
        schema.shape({
          [CRITERIA_FIELDS_KEYS.ORDER_PURCHASE_DATE]: yup.object().shape({
            select: yup.mixed(),
            from: yup.string().required(requiredErrorMessage),
            to: yup.string().required(requiredErrorMessage),
          }),
        }),
      otherwise: (schema) => schema,
    }),
  })
