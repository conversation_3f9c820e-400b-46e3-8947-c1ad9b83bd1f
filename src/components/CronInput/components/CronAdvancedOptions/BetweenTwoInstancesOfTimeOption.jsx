import React, { useState, useEffect } from "react"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"
import { TimeInstanceSelectInput } from "./TimeInstanceSelectInput"
import { CronOption, CronOptionTextContainer } from "../CronOptions"
import l from "utils/intl"

const BetweenTwoInstancesOfTimeOption = ({
  isVisible,
  selectedMode,
  settings,
  initialValues,
}) => {
  const {
    optionKey,
    titleKey,
    startLabel,
    startOptions,
    finishLabel,
    finishOptions,
    onChange,
  } = settings

  const isSelected = selectedMode === optionKey
  const isDisabled = !isSelected

  const betweenTwoInstancesOfTimeTitle = `Every ${titleKey} between {start} and {finish}`

  const startDefaultValue = startOptions?.[0]?.value || startOptions?.[0]
  const finishDefaultValue = finishOptions?.[0]?.value || finishOptions?.[0]

  const [startValue, setStartValue] = useState(
    parseInt(initialValues?.start || startDefaultValue)
  )
  const [finishValue, setFinishValue] = useState(
    parseInt(initialValues?.finish || finishDefaultValue)
  )

  useEffect(() => {
    if (onChange && isSelected) {
      onChange({
        start: startValue,
        finish: finishValue,
      })
    }
  }, [startValue, finishValue, onChange, isSelected])

  if (!isVisible) {
    return null
  }

  return (
    <CronOption value={optionKey} isActive={isSelected} hasBottomBorder>
      <CronOptionTextContainer withSpace>
        <FormattedMessage
          id={betweenTwoInstancesOfTimeTitle}
          defaultMessage={betweenTwoInstancesOfTimeTitle}
          values={{
            start: (
              <TimeInstanceSelectInput
                label={l(startLabel)}
                value={startValue}
                onChange={setStartValue}
                options={startOptions}
                isDisabled={isDisabled}
              />
            ),
            finish: (
              <TimeInstanceSelectInput
                label={l(finishLabel)}
                value={finishValue}
                onChange={setFinishValue}
                options={finishOptions}
                isDisabled={isDisabled}
              />
            ),
          }}
        />
      </CronOptionTextContainer>
    </CronOption>
  )
}

BetweenTwoInstancesOfTimeOption.propTypes = {
  isVisible: PropTypes.bool,
  selectedMode: PropTypes.string.isRequired,
  settings: PropTypes.object,
  initialValues: PropTypes.object,
}

BetweenTwoInstancesOfTimeOption.defaultProps = {
  isVisible: false,
  settings: null,
  initialValues: {},
}

export { BetweenTwoInstancesOfTimeOption }
