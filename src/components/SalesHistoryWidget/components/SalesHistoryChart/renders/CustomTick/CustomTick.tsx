import React, { ReactNode } from "react"
import { Box, Popover, Typography } from "@develop/fe-library"
import cn from "classnames"

import { TICK_COUNT_Y } from "constants/chart"

import { formatYAxis } from "../../utils"

import { CustomTickOwnProps, CustomTickProps } from "./CustomTickTypes"

import styles from "./customTick.module.scss"

export const CustomTick =
  ({ locale, currencyCode }: CustomTickProps) =>
  ({
    x,
    y,
    payload,
    width,
    index,
    fill,
    orientation,
    height,
  }: CustomTickOwnProps) => {
    const isPopoverContentVisible: boolean =
      index + 1 !== TICK_COUNT_Y &&
      (payload.value > 1000 || payload.value < -1000)
    const popoverContent: ReactNode = isPopoverContentVisible ? (
      <Typography variant="--font-body-text-9">{payload.value}</Typography>
    ) : null
    const isOrientationLeft = orientation === "left"
    const xPosition: number = isOrientationLeft ? 0 : x
    const axisPosition = isOrientationLeft ? "left" : "right"

    return (
      <foreignObject
        fill={fill}
        width={width}
        x={xPosition}
        y={y - 9}
        className={cn("recharts-cartesian-axis-tick-value", styles.customTick, {
          [styles.customTickLeft]: isOrientationLeft,
        })}
      >
        <Popover content={popoverContent} placement="top">
          <Box display="block" height={height} width="auto">
            {formatYAxis({
              value: payload.value,
              index,
              axisPosition,
              locale,
              currencyCode,
            })}
          </Box>
        </Popover>
      </foreignObject>
    )
  }
