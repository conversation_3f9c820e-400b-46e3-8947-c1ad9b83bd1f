import React from "react"
import { Box, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { SubCategory } from "../SubCategory"

import { CategoryProps } from "./CategoryTypes"

export const Category = ({
  name,
  id,
  children,
  onSubCategoryChange,
  currentSettings,
}: CategoryProps) => {
  return (
    <Box key={id} flexDirection="column" gap="m">
      {name ? (
        <Typography color="--color-text-main" variant="--font-body-text-5">
          {l(name)}
        </Typography>
      ) : null}

      <Box display="block">
        {children.map((subCategory) => {
          return (
            <SubCategory
              key={subCategory.id}
              isChecked={!!currentSettings?.[subCategory.id]}
              subCategory={subCategory}
              onSubCategoryChange={onSubCategoryChange}
            />
          )
        })}
      </Box>
    </Box>
  )
}
