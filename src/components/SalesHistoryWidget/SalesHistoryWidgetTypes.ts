import { SALES_HISTORY_STATISTIC_METRICS_KEYS } from "constants/salesHistory"

type StatisticMetricsKeys = keyof typeof SALES_HISTORY_STATISTIC_METRICS_KEYS

export type SalesHistoryUnit = {
  orders: number
  ordersCanceled: number
  units: number
  promo: number
  refunds: number
}

export type SalesHistoryCategories = Record<string, SalesHistoryCategory>

export type SalesHistoryCategory = {
  id: string
  name: string
  depth: number
  amount: number
  color_hex: string | null
  tags?: string[]
  count_transactions?: number
  type: string
  is_default: boolean
  children: SalesHistoryCategory[]
  hasChildren: boolean
  has_children?: boolean
  // Additional fields
  isChecked?: boolean
  color?: string
  parent?: string
}

export type StatisticMetric = {
  id: StatisticMetricsKeys
  name: string
  type: "pct" | "count" | "money"
  amount: number
}

export type StatisticBaseMetric = StatisticMetric & {
  prevAmount: number
  prevComparePercents: number
  subMetrics?: StatisticMetric[]
}

export type DataSeriesCategories = Record<string, number>

export type SalesHistoryDataItem = {
  date: string
  dateStart: string
  dateEnd: string
  salesCategories: DataSeriesCategories
  units: SalesHistoryUnit
  estimatedProfit?: number
  expenses: number
  revenue: number
}

export type SalesHistoryDataItemExtended = SalesHistoryDataItem & {
  dateRange?: string
}

export type WidgetStatistics = {
  metrics: StatisticMetric[]
}

export type ChartUrlParams = {
  from: string
  to: string
}

export type SalesHistoryCategoriesSelection = Record<string, boolean>

export type SalesHistorySettings = {
  id?: string
  value: SalesHistoryCategoriesSelection | null
  columns: Record<string, string> | null
  defaultSettings: SalesHistoryCategoriesSelection | null
}

export type SalesHistoryCategoriesSelectionResponse = {
  id?: string
  settings: {
    value: SalesHistoryCategoriesSelection | null
  }
}
