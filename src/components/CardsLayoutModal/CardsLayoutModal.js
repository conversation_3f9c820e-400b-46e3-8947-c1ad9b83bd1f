import React, { useState, useCallback } from "react"
import { Modal } from "@develop/fe-library"
import PropTypes from "prop-types"
import FormattedMessage from "components/FormattedMessage"

import CardsLayoutFormik from "components/CardsLayoutModal/CardsLayoutModalFormik"
import { setConfirm, FORM_IS_CHANGED } from "utils/confirm"

import styles from './cardsLayoutModal.module.scss'

const CardsLayoutModal = ({
  buttons,
  className,
  closeAfterSubmit,
  controls,
  disabled,
  disableSubmitButton,
  initialValues,
  onClose: propOnClose,
  onSubmit,
  submitButtonLabel,
  title,
  titleValues = {},
  validationSchema,
  onFormChange,
  canSubmitAfterChange,
  withCloseConfirm,
  managePermission,
  popoverMessage,
  isModalVisible,
}) => {
  const [formIsChanged, setFormIsChanged] = useState(false)
  const onClose = useCallback(() => {
    if (withCloseConfirm && formIsChanged) {
      setConfirm({
        template: FORM_IS_CHANGED,
        onOk: propOnClose,
      })
      return
    }
    propOnClose()
  }, [propOnClose, withCloseConfirm, formIsChanged])

  return (
    <>
      <Modal
        title={
          <FormattedMessage
            defaultMessage={title}
            id={title}
            values={titleValues}
          />
        }
        visible={isModalVisible}
        onClose={onClose}
        isKeyboard={false}
        bodyClassName={styles.modalContent}
      >
        <CardsLayoutFormik
          buttons={buttons}
          closeAfterSubmit={closeAfterSubmit}
          controls={controls}
          disabled={disabled}
          disableSubmitButton={disableSubmitButton}
          initialValues={initialValues}
          onClose={onClose}
          onSubmit={onSubmit}
          submitButtonLabel={submitButtonLabel}
          validationSchema={validationSchema}
          onFormChange={(isChanged, values) => {
            setFormIsChanged(isChanged)
            onFormChange && onFormChange(isChanged, values)
          }}
          canSubmitAfterChange={canSubmitAfterChange}
          managePermission={managePermission}
          popoverMessage={popoverMessage}
        />
      </Modal>
    </>
  )
}

CardsLayoutModal.propTypes = {
  buttons: PropTypes.array,
  closeAfterSubmit: PropTypes.bool,
  controls: PropTypes.array.isRequired,
  disabled: PropTypes.bool,
  disableSubmitButton: PropTypes.bool,
  initialValues: PropTypes.object,
  maskStyle: PropTypes.object,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  titleValues: PropTypes.object,
  validationSchema: PropTypes.object,
  width: PropTypes.string,
  withCloseConfirm: PropTypes.bool,
  isModalVisible: PropTypes.bool.isRequired,
}

export default CardsLayoutModal
