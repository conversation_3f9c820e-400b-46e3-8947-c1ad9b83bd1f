import React from "react"
import PropTypes from "prop-types"
import cn from "classnames"
import { Alert, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { ProductBulkEditHeaderProps } from "./ProductBulkEditTypes"

import styles from "./productBulkEdit.module.scss"

export const ProductBulkEditHeader = ({
  secondHeadingClassName = "",
  selectedCount = null,
  isSelectedTotalCount = false,
  message = "By confirming you are going to apply the following changes to {number} products",
  singleMessage = "By confirming you are going to apply the following changes to the selected product",
  selectedAllMessage = "By continuing you are going to apply the following changes to all products",
}: ProductBulkEditHeaderProps) => {
  const signleOrMultipleMessage: string =
    selectedCount === 1 ? singleMessage : message

  const fieldsTitle: string = isSelectedTotalCount
    ? selectedAllMessage
    : signleOrMultipleMessage

  return (
    <>
      <Alert
        alertType="warning"
        message={
          <Typography variant="--font-body-text-7" color="--color-text-main">
            {l(
              "Please note, any edits to this field will re-write settings for all current costs presented on this page for selected products",
            )}
          </Typography>
        }
      />
      <div className={cn(styles.bulkFieldTitle, secondHeadingClassName)}>
        {l(fieldsTitle, { number: selectedCount })}
      </div>
    </>
  )
}

ProductBulkEditHeader.propTypes = {
  secondHeadingClassName: PropTypes.string,
  isSelectedTotalCount: PropTypes.number,
  selectedCount: PropTypes.bool,
  message: PropTypes.string,
  selectedAllMessage: PropTypes.string,
}
