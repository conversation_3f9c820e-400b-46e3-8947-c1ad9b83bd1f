import React, { useEffect, useRef, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import withSizes from "react-sizes"
import cn from "classnames"
import { useFormikContext } from "formik"

import productTagColorActions from "actions/productTagColorActions"
import productTagsActions from "actions/productTagsActions"

import { productTagColorsSelector } from "selectors/productTagColorsSelectors"
import { productTagsSelector } from "selectors/productTagsSelectors"

import { getBreakpoint, lowerThan, SM } from "utils/breakpoints"
import { generateRandomColor } from "utils/colors"
import l from "utils/intl"

import FormLine from "../FormLine"

import styles from "./productBulk.module.scss"

const { createProductTag, getProductTags, displayCreateProductTagModal } =
  productTagsActions
const { getProductTagColors } = productTagColorActions
// Copied form Repricer will refact later
const ProductTagBulkContainer = ({ breakpoint }) => {
  const [addTagValue, setAddTagValue] = useState([])
  const [removeTagValue, setRemoveTagValue] = useState([])
  const [searchAddTagValue, setSearchAddTagValue] = useState(null)
  const [searchRemoveTagValue, setSearchRemoveTagValue] = useState(null)
  const [focusedAdd, setFocusedAdd] = useState(false)
  const [focusedRemove, setFocusedRemove] = useState(false)
  const [tagsAmount, setTagsAmount] = useState(0)
  const [errorMessage, setErrorMessage] = useState("")
  const [dropDownVisivle, setDropdownVisible] = useState(false)

  const { setValues, values } = useFormikContext()

  const blurRef = useRef(null)

  const tags = useSelector(productTagsSelector)
  const productTagColors = useSelector(productTagColorsSelector)

  const dispatch = useDispatch()

  const noChanges = { value: undefined, status: "no_change" }

  const addProductTagHandler = (payload, successCallback, failureCallback) => {
    dispatch(createProductTag({ payload, successCallback, failureCallback }))
  }

  const getProductTagsHandler = () => {
    dispatch(getProductTags({}))
  }

  useEffect(() => {
    setTagsAmount(tags.length)
    dispatch(getProductTagColors({}))
  }, [])

  useEffect(() => {
    if (!values.tags_add) {
      setAddTagValue([])
    }
    if (!values.tags_remove) {
      setRemoveTagValue([])
    }
  }, [values?.tags_add, values?.tags_remove])

  useEffect(() => {
    if (tagsAmount !== 0 && tags.length > tagsAmount) {
      setAddTagValue([...addTagValue, tags[tags.length - 1].id])
      setValues({
        ...values,
        tags_add: [...addTagValue, tags[tags.length - 1].id],
      })
    }
  }, [tags])

  const addNewTag = () => {
    setDropdownVisible(true)
    const randomColor = generateRandomColor(productTagColors)

    addProductTagHandler(
      { title: searchAddTagValue, color: randomColor },
      () => {
        getProductTagsHandler()
        setSearchAddTagValue(null)
        blurRef.current.focus()
      },
      (error) => {
        setErrorMessage(error.map(({ message }) => message))
      },
    )
  }
  const handleChange = (value, fieldName) => {
    setErrorMessage("")
    if (fieldName === "tags_add") {
      setAddTagValue(value)
      setSearchAddTagValue(null)
      if (!value.length) {
        setValues({
          ...values,
          tags_add: value,
          _bulkChanges: { tags_add: noChanges },
        })
      } else {
        setValues({ ...values, tags_add: value })
      }
    }
    if (fieldName === "tags_remove") {
      setRemoveTagValue(value)
      setSearchRemoveTagValue(null)
      if (!value.length) {
        setValues({
          ...values,
          tags_remove: value,
          _bulkChanges: { tags_remove: noChanges },
        })
      } else {
        setValues({ ...values, tags_remove: value })
      }
    }
  }
  const onKeyPress = (e) => {
    if (e.keyCode === 13) {
      e.stopPropagation()
      if (searchAddTagValue && searchAddTagValue.trim().length) {
        addNewTag(searchAddTagValue, () => {
          setSearchAddTagValue(null)
        })
      }
    }
  }

  const addOnBlur = () => {
    setFocusedAdd(false)
    if (errorMessage) {
      setErrorMessage("")
      setSearchAddTagValue(null)
    }
  }
  const onAddFocus = () => {
    setFocusedAdd(true)
    setDropdownVisible(false)
  }
  const inputs = [
    {
      type: "selectWithSearch",
      placeholder: !errorMessage && l("Assign tag"),
      name: "tags_add",
      mode: "multiple",
      value: addTagValue,
      searchValue: searchAddTagValue,
      onChange: (value) => handleChange(value, "tags_add"),
      onSearch: (value) => setSearchAddTagValue(value),
      onFocus: () => onAddFocus(),
      onBlur: () => addOnBlur(),
      options: tags.map(({ id, title, color }) => ({
        title,
        value: id,
        color,
        withMarker: true,
      })),
      withoutBulkClear: true,
      className: "input",
      withHiddenValues: true,
      focused: focusedAdd,
      error: errorMessage,
      searchRow: (
        <div className={styles.selectItem} onClick={addNewTag}>
          {searchAddTagValue}
        </div>
      ),
      dropdownClassName: cn(styles.dropdown, {
        [styles.closed]: dropDownVisivle,
      }),
      onInputKeyDown: (e) => onKeyPress(e),
    },
    {
      type: "selectWithSearch",
      placeholder: l("Remove tag"),
      name: "tags_remove",
      mode: "multiple",
      value: removeTagValue,
      searchValue: searchRemoveTagValue,
      onChange: (value) => handleChange(value, "tags_remove"),
      onSearch: (value) => setSearchRemoveTagValue(value),
      onFocus: () => setFocusedRemove(true),
      onBlur: () => setFocusedRemove(false),
      options: tags.map(({ id, title, color }) => ({
        title,
        value: id,
        color,
        withMarker: true,
      })),
      className: "input",
      withHiddenValues: true,
      focused: focusedRemove,
      isSecondTagField: true,
    },
  ]

  return (
    <>
      <FormLine
        isCreateTagButton
        className={cn(styles.formLine, { [styles.error]: errorMessage })}
        inputs={lowerThan(breakpoint, SM) ? inputs.reverse() : inputs}
        buttons={[
          {
            withTooltip: true,
            content: l("Create"),
            icon: "icnPlus",
            isCreateTagButton: true,
            onClick: () => {
              dispatch(displayCreateProductTagModal((true, {})))
            },
          },
        ]}
      />
      <input ref={blurRef} className={styles.blurInput} />
    </>
  )
}

const mapSizesToProps = ({ width }) => ({
  breakpoint: getBreakpoint(width),
})

export const ProductTagBulk = withSizes(mapSizesToProps)(
  ProductTagBulkContainer,
)
