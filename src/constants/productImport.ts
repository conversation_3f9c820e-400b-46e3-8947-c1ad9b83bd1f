export const TABLE_SETTINGS_KEY = "productImportTableSettings"

export const STATUS_TYPES = {
  new: "new",
  inProgress: "in_progress",
  done: "done",
  noItems: "no_items",
} as const

export const FILTER_TYPES = {
  unknown: "unknown",
  bulk_edit: "bulk_edit",
  auto: "auto",
  manual: "manual",
}

export const STATUS_NAMES = {
  [STATUS_TYPES.new]: "New",
  [STATUS_TYPES.inProgress]: "In progress",
  [STATUS_TYPES.done]: "Done",
  [STATUS_TYPES.noItems]: "No products",
}

export const FILTER_TYPES_MAP = {
  unknown: { label: "Import", value: FILTER_TYPES.unknown },
  bulk_edit: { label: "Bulk edit", value: FILTER_TYPES.bulk_edit },
  auto: { label: "Auto import", value: FILTER_TYPES.auto },
  manual: { label: "Manual import", value: FILTER_TYPES.manual },
}

export const TEMPLATE_HANDLER_NAMES = {
  productCostPeriods: "product_cost_periods",
}

export const TEMPLATE_HANDLER_VALUES = {
  PRODUCT_COST_PERIODS: "product_cost_periods",
  ORDER_FBM_COST: "order_fbm_cost",
  ORDER_ITEM_FBM_COST: "order_item_fbm_cost",
} as const

export const TEMPLATE_HANDLER_NAME_TITLES = {
  [TEMPLATE_HANDLER_VALUES.PRODUCT_COST_PERIODS]: "Product costs template",
  [TEMPLATE_HANDLER_VALUES.ORDER_FBM_COST]: "Order costs template",
  [TEMPLATE_HANDLER_VALUES.ORDER_ITEM_FBM_COST]: "Order item costs template",
} as const

export const FILE_HANDLER_NAME_TITLES = {
  [TEMPLATE_HANDLER_VALUES.PRODUCT_COST_PERIODS]: "Product costs",
  [TEMPLATE_HANDLER_VALUES.ORDER_FBM_COST]: "Order costs",
  [TEMPLATE_HANDLER_VALUES.ORDER_ITEM_FBM_COST]: "Order item costs",
} as const

export const OPTIONS_TEMPLATE_HANDLER_NAMES = [
  {
    label:
      TEMPLATE_HANDLER_NAME_TITLES[
        TEMPLATE_HANDLER_VALUES.PRODUCT_COST_PERIODS
      ],
    value: TEMPLATE_HANDLER_VALUES.PRODUCT_COST_PERIODS,
  },
  {
    label: TEMPLATE_HANDLER_NAME_TITLES[TEMPLATE_HANDLER_VALUES.ORDER_FBM_COST],
    value: TEMPLATE_HANDLER_VALUES.ORDER_FBM_COST,
  },
  {
    label:
      TEMPLATE_HANDLER_NAME_TITLES[TEMPLATE_HANDLER_VALUES.ORDER_ITEM_FBM_COST],
    value: TEMPLATE_HANDLER_VALUES.ORDER_ITEM_FBM_COST,
  },
]

export const OPTIONS_FILE_HANDLER_NAMES = [
  {
    label:
      FILE_HANDLER_NAME_TITLES[TEMPLATE_HANDLER_VALUES.PRODUCT_COST_PERIODS],
    value: TEMPLATE_HANDLER_VALUES.PRODUCT_COST_PERIODS,
  },
  {
    label: FILE_HANDLER_NAME_TITLES[TEMPLATE_HANDLER_VALUES.ORDER_FBM_COST],
    value: TEMPLATE_HANDLER_VALUES.ORDER_FBM_COST,
  },
  {
    label:
      FILE_HANDLER_NAME_TITLES[TEMPLATE_HANDLER_VALUES.ORDER_ITEM_FBM_COST],
    value: TEMPLATE_HANDLER_VALUES.ORDER_ITEM_FBM_COST,
  },
]
