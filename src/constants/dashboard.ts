import { INPUT_MODE } from "./dateRange"
import { SESSION_STORAGE_KEYS } from "./sessionStorage"

import {
  DashboardFiltersParamsKeysType,
  DashboardTabsInitialUrlParams,
} from "types/UrlParams"

export const DASHBOARD_MANDATORY_URL_PARAMS: DashboardFiltersParamsKeysType[] =
  ["inputMode", "from", "to", "currency_code", "view"]

export const DASHBOARD_SIDEBAR_TABS = {
  CLOSED: "",
  FILTERS: "filters",
  SEGMENTS: "segments",
} as const

export const DASHBOARD_TABS_KEYS = {
  PROFIT: "profit",
  INSIGHTS: "insights",
  STATISTICS: "statistics",
} as const

export const DASHBOARD_USER_SESSION_BY_TAB = {
  [DASHBOARD_TABS_KEYS.PROFIT]:
    SESSION_STORAGE_KEYS.DASHBOARD_PROFIT_URL_PARAMS,
  [DASHBOARD_TABS_KEYS.INSIGHTS]:
    SESSION_STORAGE_KEYS.DASHBOARD_INSIGHTS_URL_PARAMS,
  [DASHBOARD_TABS_KEYS.STATISTICS]:
    SESSION_STORAGE_KEYS.DASHBOARD_STATISTICS_URL_PARAMS,
} as const

export const DASHBOARD_DEFAULT_VALUES_STATUS = {
  URL_PARAMS_INITIALIZE: "urlParamsInitialize",
  URL_PARAMS_READY_TO_SET: "urlParamsReadyToSet",
  URL_PARAMS_DONE: "urlParamsDone",
} as const

export const DEFAULT_INPUT_MODE = INPUT_MODE.CURRENT_MONTH

export const DASHBOARD_TABS_INITIAL_URL_PARAMS: DashboardTabsInitialUrlParams =
  {
    [DASHBOARD_TABS_KEYS.PROFIT]: {
      inputMode: DEFAULT_INPUT_MODE,
    },
  }
