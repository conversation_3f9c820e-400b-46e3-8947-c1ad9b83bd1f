import { InputMode } from "@develop/fe-library"

export const CLEAR_TO_CLOSE_LABEL = { clear: "Close" } as const

export const INPUT_MODE = {
  EQUALS: "equals",
  AFTER: "after",
  BEFORE: "before",
  BET<PERSON>EEN: "between",
  ON_OR_AFTER: "onOrAfter",
  ON_OR_BEFORE: "onOrBefore",
  PREVIOUS: "previous",
  ALL_TIME: "allTime",
  CURRENT_MONTH: "currentMonth",
  CURRENT_WEEK: "currentWeek",
  CURRENT_YEAR: "currentYear",
  LAST_14_DAYS: "last14days",
  LAST_30_DAYS: "last30days",
  LAST_6_MONTHS: "last6months",
  LAST_7_DAYS: "last7days",
  LAST_MONTH: "lastMonth",
  LAST_90_DAYS: "last90days",
  LAST_WEEK: "lastWeek",
  LAST_YEAR: "lastYear",
  TODAY: "today",
  YESTERDAY: "yesterday",
} as const

export const INPUT_MODES_TYPES: InputMode[] = [
  INPUT_MODE.AFTER,
  INPUT_MODE.BEFORE,
  INPUT_MODE.BETWEEN,
  INPUT_MODE.EQUALS,
  INPUT_MODE.ON_OR_AFTER,
  INPUT_MODE.ON_OR_BEFORE,
  INPUT_MODE.PREVIOUS,
]

export const INPUT_MODES_PRESETS: InputMode[] = [
  INPUT_MODE.ALL_TIME,
  INPUT_MODE.CURRENT_MONTH,
  INPUT_MODE.CURRENT_WEEK,
  INPUT_MODE.CURRENT_YEAR,
  INPUT_MODE.LAST_14_DAYS,
  INPUT_MODE.LAST_30_DAYS,
  INPUT_MODE.LAST_6_MONTHS,
  INPUT_MODE.LAST_7_DAYS,
  INPUT_MODE.LAST_MONTH,
  INPUT_MODE.LAST_90_DAYS,
  INPUT_MODE.LAST_WEEK,
  INPUT_MODE.LAST_YEAR,
  INPUT_MODE.TODAY,
  INPUT_MODE.YESTERDAY,
]

export const INPUT_MODES: InputMode[] = [
  ...INPUT_MODES_TYPES,
  ...INPUT_MODES_PRESETS,
]
