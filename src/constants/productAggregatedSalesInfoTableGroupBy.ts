import { ProductAggregatedSalesInfoTableGroupBy } from "types/ProductAggregatedSalesInfoTableGroupBy"

export const PRODUCT_AGGREGATED_SALES_INFO_TABLE_GROUP_BY: Record<
  ProductAggregatedSalesInfoTableGroupBy,
  ProductAggregatedSalesInfoTableGroupBy
> = {
  asin: "asin",
  marketplace: "marketplace",
  brand: "brand",
  manufacturer: "manufacturer",
  product_type: "product_type",
  fulfillment_method: "fulfillment_method",
}

export const PRODUCT_AGGREGATED_SALES_INFO_TABLE_GROUP_BY_LABELS = {
  asin: "ASIN",
  marketplace: "Marketplace",
  brand: "Brand",
  manufacturer: "Manufacturer",
  product_type: "Product type",
  fulfillment_method: "Fulfillment method",
}
