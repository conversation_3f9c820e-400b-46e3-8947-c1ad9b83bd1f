@import 'assets/styles/variables.scss';

@mixin fontStyles {
  font-size: 13px;
  font-family: Roboto, sans-serif;
  font-weight: 400;
}

.ant-select, .ant-select-selection {
  @include fontStyles;
}
.ant-input.ant-input {
  color: $text_main;
  height: 32px;
  &::placeholder {
    color: $text_placeholders
  }
}

.ant-input{
  &[type=text]{
    @include fontStyles;
  }
  &[type=number]{
    @include fontStyles;
  }
}

/*  Hide Arrows on Number fields */
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button,
.ant-input,
.ant-calendar-input {
  appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}


.ant-select-dropdown {
  .ant-select-dropdown-menu {
    max-height: 260px;

    .ant-select-dropdown-menu-item-selected {
      color: $border_active;
    }
  }
}

.ant-checkbox-checked.ant-checkbox-checked {
  &.ant-checkbox-disabled {
    .ant-checkbox-inner {
      background-color: $int_active_on;
      border-color: $border_active !important;
      opacity: 0.3;

      &:after {
        border-color: $int_bg_second;
      }
    }
  }
}

.ant-checkbox.ant-checkbox {
  &.ant-checkbox-disabled {
    + span {
      cursor: default;
    }

    .ant-checkbox-input {
      cursor: default;
    }
  }
}

.ant-checkbox-wrapper.ant-checkbox-wrapper,
.ant-checkbox.ant-checkbox {
  cursor: default;
}

.ant-select-disabled.ant-select-disabled {
  .ant-select-selection {
    cursor: default;
  }
}

.ant-input.ant-input[disabled] {
  color: $text_disable;
  cursor: default;

  &+ .anticon {
    cursor: default;
  }
}

.ant-calendar-range-picker-input {
  line-height: normal;
}

.ant-select-selection--multiple.ant-select-selection--multiple {
  .ant-select-selection__choice {
    &:nth-last-child(2) {
      margin-bottom: 3px;
    }

    &:first-child {
      margin-bottom: 0;
    }
  }
  .ant-select-dropdown-menu-item-active {
    & .anticon {
      display: none
    }
    &.ant-select-dropdown-menu-item-selected {
      & .anticon {
        display: block
      }
    }
  }  
}

.ant-select-dropdown--multiple {
  .ant-select-dropdown-menu-item-active {
    & .anticon {
      display: none
    }
    &.ant-select-dropdown-menu-item-selected {
      & .anticon {
        display: block
      }
    }
  }  
}

.ant-select-search__field {
  padding: 0;
} 

.ant-input-affix-wrapper {
  padding: 0;
  .ant-input.ant-input {
    height: 30px;
    padding-left: 11px;
    padding-right: 11px;
  }
  .ant-input-suffix {
    margin-right: 10px;
  }
}

.ant-dropdown-menu-submenu-popup ul {
  margin-top: -8px;
  margin-right: 0;
  margin-left: 0;
  &::before {
    content: '';
    position: absolute;
    display: block;
    left: -5px;
    bottom: 0;
    top: 0;
    right: 0;
    z-index: -1;
  }
}

.ant-select-selection-item.ant-select-selection-item {
  text-align: left;
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  .ant-select-item-option-content,
  .anticon {
    color: $icon_active;
  }
  
}
