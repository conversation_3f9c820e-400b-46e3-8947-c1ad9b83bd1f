import { createSelector } from 'reselect'
import { 
  currentUserSelector, 
  timezoneSelector as mainTimezoneSelector
} from 'selectors/mainStateSelectors'

const defaultTimezone = 'Europe/Berlin'

export const timezones = state => mainTimezoneSelector(state).timezoneOptions
export const userTimezoneSelector = createSelector(
  currentUserSelector,
  currentUser => currentUser?.timezone?.name || defaultTimezone
)

const timezoneSelector = createSelector(timezones, tz =>
  tz.map(x => ({ label: x.name, value: x.id }))
)

export const timeZoneSelectorAlphabetOrder = createSelector(
  timezoneSelector,
  timezones =>
    timezones.sort(({ label: labelA }, { label: labelB }) => {
      if (labelA < labelB) {
        return -1
      } else if (labelA > labelB) {
        return 1
      } else {
        return 0
      }
    })
)

export default timezoneSelector
