{"plugins": ["react", "import", "unused-imports", "simple-import-sort", "prettier"], "extends": ["react-app", "react-app/jest", "prettier"], "parser": "@typescript-eslint/parser", "rules": {"react/jsx-key": "error", "prefer-destructuring": ["warn", {"array": false, "object": true}], "prettier/prettier": ["error", {"endOfLine": "auto"}], "no-nested-ternary": "warn", "no-else-return": "warn", "default-param-last": "warn", "arrow-body-style": "off", "no-console": ["warn", {"allow": ["warn", "error", "info"]}], "react/no-array-index-key": "warn", "react/jsx-sort-props": ["warn", {"callbacksLast": true, "shorthandFirst": true, "multiline": "last", "ignoreCase": true, "noSortAlphabetically": false, "reservedFirst": true}], "simple-import-sort/imports": ["error", {"groups": [["react-app-polyfill/stable", "^react", "^antd", "^@*", "^@develop", "^\\u0000", "^node:", "prop-types", "^(?!\\.)"], ["^config"], ["^middleware"], ["^api"], ["^initialState"], ["^actions"], ["^reducers"], ["^selectors"], ["^pages"], ["^components"], ["^hocs"], ["^hooks"], ["^utils"], ["^consts"], ["^constants"], ["^models"], ["^validationSchemas"], ["^assets"], ["^\\..*components$"], ["^\\..*hooks$"], ["^\\..*utils$"], ["^\\..*consts$"], ["^\\..*constants$"], ["^\\.\\./.*components$"], ["^\\.\\./.*hooks$"], ["^\\.\\./.*utils$"], ["^\\.\\./.*consts$"], ["^\\.\\./.*constants$"], ["^\\./[^../]"], ["^\\.\\./"], ["^components/.*[Tt]ypes$"], ["^hooks/.*[Tt]ypes$"], ["^utils/.*[Tt]ypes$"], ["^consts/.*[Tt]ypes$"], ["^constants/.*[Tt]ypes$"], ["^types", "^interfaces"], ["^\\./.*[Tt]ypes$", "^\\.\\./.*[Tt]ypes$", "Types"], ["^\\./[^/]+\\.module\\.s?css$", "^\\.\\./[^/]+/[^/]+\\.module\\.s?css$", "^\\.\\./(\\.\\./)+.*\\.module\\.s?css$"], ["^\\./.*\\.(css|scss)$", "^\\.\\./.*\\.(css|scss)$", "(css|scss)"]]}], "simple-import-sort/exports": "error", "import/no-duplicates": "error", "unused-imports/no-unused-imports": "error", "import/newline-after-import": "error", "react/jsx-boolean-value": ["error", "never"], "react/function-component-definition": [2, {"namedComponents": "arrow-function", "unnamedComponents": "arrow-function"}], "import/extensions": ["off", "ignorePackages", {"js": "never", "jsx": "never", "ts": "never", "tsx": "never"}], "import/no-extraneous-dependencies": ["off"], "import/prefer-default-export": ["off"], "padding-line-between-statements": ["error", {"blankLine": "always", "prev": "*", "next": "return"}, {"blankLine": "always", "prev": ["const", "let", "var"], "next": "*"}, {"blankLine": "any", "prev": ["const", "let", "var"], "next": ["const", "let", "var"]}], "react/jsx-pascal-case": ["off"]}}